2025-07-11 00:01:58,067 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 211 points
2025-07-11 00:01:58,067 - [BITVAVO] - root - INFO - DOT/USDT B&H total return: -19.90%
2025-07-11 00:01:58,071 - [BIT<PERSON><PERSON>] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-11 00:01:58,085 - [BIT<PERSON><PERSON>] - root - INFO - Configuration loaded successfully.
2025-07-11 00:01:58,098 - [BITVAVO] - root - INFO - Using colored segments for single-asset strategy visualization
2025-07-11 00:01:58,239 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-11 00:01:58,254 - [B<PERSON><PERSON><PERSON>] - root - INFO - Configuration loaded successfully.
2025-07-11 00:02:00,667 - [<PERSON>ITVA<PERSON>] - root - INFO - Added ETH/USDT buy-and-hold curve with 211 points
2025-07-11 00:02:00,667 - [<PERSON><PERSON><PERSON><PERSON>] - root - INFO - Added BTC/USDT buy-and-hold curve with 211 points
2025-07-11 00:02:00,667 - [BITVAVO] - root - INFO - Added SOL/USDT buy-and-hold curve with 211 points
2025-07-11 00:02:00,667 - [BITVAVO] - root - INFO - Added SUI/USDT buy-and-hold curve with 211 points
2025-07-11 00:02:00,667 - [BITVAVO] - root - INFO - Added XRP/USDT buy-and-hold curve with 211 points
2025-07-11 00:02:00,668 - [BITVAVO] - root - INFO - Added AAVE/USDT buy-and-hold curve with 211 points
2025-07-11 00:02:00,668 - [BITVAVO] - root - INFO - Added AVAX/USDT buy-and-hold curve with 211 points
2025-07-11 00:02:00,668 - [BITVAVO] - root - INFO - Added ADA/USDT buy-and-hold curve with 211 points
2025-07-11 00:02:00,668 - [BITVAVO] - root - INFO - Added LINK/USDT buy-and-hold curve with 211 points
2025-07-11 00:02:00,668 - [BITVAVO] - root - INFO - Added TRX/USDT buy-and-hold curve with 211 points
2025-07-11 00:02:00,668 - [BITVAVO] - root - INFO - Added PEPE/USDT buy-and-hold curve with 211 points
2025-07-11 00:02:00,668 - [BITVAVO] - root - INFO - Added DOGE/USDT buy-and-hold curve with 211 points
2025-07-11 00:02:00,668 - [BITVAVO] - root - INFO - Added BNB/USDT buy-and-hold curve with 211 points
2025-07-11 00:02:00,668 - [BITVAVO] - root - INFO - Added DOT/USDT buy-and-hold curve with 211 points
2025-07-11 00:02:00,668 - [BITVAVO] - root - INFO - Added 14 buy-and-hold curves to results
2025-07-11 00:02:00,668 - [BITVAVO] - root - INFO -   - ETH/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 00:02:00,668 - [BITVAVO] - root - INFO -   - BTC/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 00:02:00,669 - [BITVAVO] - root - INFO -   - SOL/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 00:02:00,669 - [BITVAVO] - root - INFO -   - SUI/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 00:02:00,669 - [BITVAVO] - root - INFO -   - XRP/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 00:02:00,669 - [BITVAVO] - root - INFO -   - AAVE/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 00:02:00,669 - [BITVAVO] - root - INFO -   - AVAX/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 00:02:00,669 - [BITVAVO] - root - INFO -   - ADA/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 00:02:00,669 - [BITVAVO] - root - INFO -   - LINK/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 00:02:00,669 - [BITVAVO] - root - INFO -   - TRX/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 00:02:00,669 - [BITVAVO] - root - INFO -   - PEPE/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 00:02:00,670 - [BITVAVO] - root - INFO -   - DOGE/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 00:02:00,670 - [BITVAVO] - root - INFO -   - BNB/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 00:02:00,670 - [BITVAVO] - root - INFO -   - DOT/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 00:02:00,693 - [BITVAVO] - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-07-11 00:02:00,693 - [BITVAVO] - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-11 00:02:00,695 - [BITVAVO] - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-07-11 00:02:00,696 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-11 00:02:00,710 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 00:02:00,711 - [BITVAVO] - root - INFO - Loaded MTPI configuration with 8 enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-11 00:02:00,711 - [BITVAVO] - root - INFO - Using MTPI indicators from config: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-11 00:02:00,711 - [BITVAVO] - root - INFO - Combination method: consensus
2025-07-11 00:02:00,711 - [BITVAVO] - root - INFO - Calculated appropriate limit for 1d timeframe: 216 candles (minimum 180.0 candles needed for 60 length indicator)
2025-07-11 00:02:00,711 - [BITVAVO] - root - INFO - Checking cache for 1 symbols (1d)...
2025-07-11 00:02:00,734 - [BITVAVO] - root - INFO - Loaded 2152 rows of BTC/USDT data from cache (last updated: 2025-07-11)
2025-07-11 00:02:00,735 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 00:02:00,735 - [BITVAVO] - root - INFO - Loaded 2152 rows of BTC/USDT data from cache (after filtering).
2025-07-11 00:02:00,735 - [BITVAVO] - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-11 00:02:00,736 - [BITVAVO] - root - INFO - Fetched BTC data: 2152 candles from 2019-08-20 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 00:02:00,736 - [BITVAVO] - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-07-11 00:02:01,135 - [BITVAVO] - root - INFO - Generated PGO Score signals: {-1: np.int64(996), 0: np.int64(34), 1: np.int64(1122)}
2025-07-11 00:02:01,136 - [BITVAVO] - root - INFO - PGO signal: 1
2025-07-11 00:02:01,136 - [BITVAVO] - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-07-11 00:02:01,258 - [BITVAVO] - root - INFO - Generated BB Score signals: {-1: np.int64(993), 0: np.int64(33), 1: np.int64(1126)}
2025-07-11 00:02:01,259 - [BITVAVO] - root - INFO - Bollinger Bands signal: 1
2025-07-11 00:02:03,777 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:02:08,187 - [BITVAVO] - root - INFO - Generated DWMA signals using Weighted SD method
2025-07-11 00:02:08,187 - [BITVAVO] - root - INFO - DWMA Score signal: 1
2025-07-11 00:02:08,831 - [BITVAVO] - root - INFO - Generated DEMA Supertrend signals
2025-07-11 00:02:08,831 - [BITVAVO] - root - INFO - Signal distribution: {-1: np.int64(1268), 0: np.int64(1), 1: np.int64(883)}
2025-07-11 00:02:08,831 - [BITVAVO] - root - INFO - Generated DEMA Super Score signals
2025-07-11 00:02:08,832 - [BITVAVO] - root - INFO - DEMA Super Score signal: 1
2025-07-11 00:02:11,037 - [BITVAVO] - root - INFO - Generated DPSD signals
2025-07-11 00:02:11,037 - [BITVAVO] - root - INFO - Signal distribution: {-1: np.int64(1087), 0: np.int64(87), 1: np.int64(978)}
2025-07-11 00:02:11,037 - [BITVAVO] - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-07-11 00:02:11,038 - [BITVAVO] - root - INFO - DPSD Score signal: 1
2025-07-11 00:02:11,156 - [BITVAVO] - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-07-11 00:02:11,156 - [BITVAVO] - root - INFO - Generated AAD Score signals using SMA method
2025-07-11 00:02:11,156 - [BITVAVO] - root - INFO - AAD Score signal: 1
2025-07-11 00:02:12,139 - [BITVAVO] - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-07-11 00:02:12,139 - [BITVAVO] - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-07-11 00:02:12,139 - [BITVAVO] - root - INFO - Dynamic EMA Score signal: 1
2025-07-11 00:02:13,724 - [BITVAVO] - root - INFO - Quantile DEMA Score signal: 1
2025-07-11 00:02:13,725 - [BITVAVO] - root - INFO - Individual signals: {'pgo': 1, 'bollinger_bands': 1, 'dwma_score': 1, 'dema_super_score': 1, 'dpsd_score': 1, 'aad_score': 1, 'dynamic_ema_score': 1, 'quantile_dema_score': 1}
2025-07-11 00:02:13,725 - [BITVAVO] - root - INFO - Combined MTPI signal (consensus): 1
2025-07-11 00:02:13,725 - [BITVAVO] - root - INFO - MTPI Score: 1.000000
2025-07-11 00:02:13,725 - [BITVAVO] - root - INFO - Added current MTPI score to results: 1.000000 (using 1d timeframe)
2025-07-11 00:02:13,729 - [BITVAVO] - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-07-11 00:02:13,730 - [BITVAVO] - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 4.0, 'SUI/USDT': 11.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 3.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 0.0}
2025-07-11 00:02:13,730 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 4.0, 'SUI/USDT': 11.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 3.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 0.0}
2025-07-11 00:02:13,730 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-11 00:02:13,730 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-11 00:02:13,730 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 9.0)
2025-07-11 00:02:13,730 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - BTC/USDT -> BTC/EUR (score: 3.0)
2025-07-11 00:02:13,730 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 4.0)
2025-07-11 00:02:13,731 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 11.0)
2025-07-11 00:02:13,731 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 10.0)
2025-07-11 00:02:13,731 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 10.0)
2025-07-11 00:02:13,731 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 1.0)
2025-07-11 00:02:13,731 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 3.0)
2025-07-11 00:02:13,731 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 8.0)
2025-07-11 00:02:13,731 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - TRX/USDT -> TRX/EUR (score: 1.0)
2025-07-11 00:02:13,731 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 9.0)
2025-07-11 00:02:13,731 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 6.0)
2025-07-11 00:02:13,731 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 0.0)
2025-07-11 00:02:13,731 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 0.0)
2025-07-11 00:02:13,731 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 4.0, 'SUI/EUR': 11.0, 'XRP/EUR': 10.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 1.0, 'ADA/EUR': 3.0, 'LINK/EUR': 8.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 6.0, 'BNB/EUR': 0.0, 'DOT/EUR': 0.0}
2025-07-11 00:02:13,731 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 00:02:13,732 - [BITVAVO] - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 4.0, 'SUI/EUR': 11.0, 'XRP/EUR': 10.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 1.0, 'ADA/EUR': 3.0, 'LINK/EUR': 8.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 6.0, 'BNB/EUR': 0.0, 'DOT/EUR': 0.0}
2025-07-11 00:02:13,738 - [BITVAVO] - root - INFO - Saved metrics to new file: Performance_Metrics/metrics_BestAsset_1d_1d_assets14_since_20250704_run_20250629_130029.csv
2025-07-11 00:02:13,738 - [BITVAVO] - root - INFO - Saved performance metrics to CSV: Performance_Metrics/metrics_BestAsset_1d_1d_assets14_since_20250704_run_20250629_130029.csv
2025-07-11 00:02:13,738 - [BITVAVO] - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-07-11 00:02:13,738 - [BITVAVO] - root - INFO - Results type: <class 'dict'>
2025-07-11 00:02:13,738 - [BITVAVO] - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-07-11 00:02:13,738 - [BITVAVO] - root - INFO - Success flag set to: True
2025-07-11 00:02:13,738 - [BITVAVO] - root - INFO - Message set to: Strategy calculation completed successfully
2025-07-11 00:02:13,738 - [BITVAVO] - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 211 entries
2025-07-11 00:02:13,738 - [BITVAVO] - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-07-11 00:02:13,738 - [BITVAVO] - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 211 entries
2025-07-11 00:02:13,739 - [BITVAVO] - root - INFO -   - mtpi_signals: <class 'pandas.core.series.Series'> with 271 entries
2025-07-11 00:02:13,739 - [BITVAVO] - root - INFO -   - mtpi_score: <class 'float'>
2025-07-11 00:02:13,739 - [BITVAVO] - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 211 entries
2025-07-11 00:02:13,739 - [BITVAVO] - root - INFO -   - performance_metrics: dict with 3 entries
2025-07-11 00:02:13,739 - [BITVAVO] - root - INFO -   - metrics_file: <class 'str'>
2025-07-11 00:02:13,739 - [BITVAVO] - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-07-11 00:02:13,739 - [BITVAVO] - root - INFO -   - success: <class 'bool'>
2025-07-11 00:02:13,739 - [BITVAVO] - root - INFO -   - message: <class 'str'>
2025-07-11 00:02:13,739 - [BITVAVO] - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-07-11 00:02:13,739 - [BITVAVO] - root - INFO - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: 
2025-07-11 00:02:13,740 - [BITVAVO] - root - INFO - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-07-06 00:00:00+00:00    
2025-07-07 00:00:00+00:00    
2025-07-08 00:00:00+00:00    
2025-07-09 00:00:00+00:00    
2025-07-10 00:00:00+00:00    
dtype: object
2025-07-11 00:02:13,740 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Latest scores available: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 4.0, 'SUI/EUR': 11.0, 'XRP/EUR': 10.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 1.0, 'ADA/EUR': 3.0, 'LINK/EUR': 8.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 6.0, 'BNB/EUR': 0.0, 'DOT/EUR': 0.0}
2025-07-11 00:02:13,740 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Scores dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 00:02:13,741 - [BITVAVO] - root - INFO - [DEBUG] TIE-BREAKING - Strategy: imcumbent
2025-07-11 00:02:13,741 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - find_best_asset_for_day() called with 14 assets
2025-07-11 00:02:13,741 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Input scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 4.0, 'SUI/EUR': 11.0, 'XRP/EUR': 10.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 1.0, 'ADA/EUR': 3.0, 'LINK/EUR': 8.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 6.0, 'BNB/EUR': 0.0, 'DOT/EUR': 0.0}
2025-07-11 00:02:13,741 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 00:02:13,741 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Maximum score found: 11.0
2025-07-11 00:02:13,741 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Assets with max score 11.0: ['SUI/EUR']
2025-07-11 00:02:13,741 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - No tie detected, single winner: SUI/EUR
2025-07-11 00:02:13,741 - [BITVAVO] - root - ERROR - [DEBUG] SELECTED BEST ASSET: SUI/EUR (score: 11.0)
2025-07-11 00:02:13,741 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Current day's best asset: SUI/EUR (MTPI signal: 1)
2025-07-11 00:02:13,741 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Maximum score: 11.0
2025-07-11 00:02:13,741 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Assets with max score: ['SUI/EUR']
2025-07-11 00:02:13,741 - [BITVAVO] - root - ERROR - [DEBUG] NO TIE - UPDATING SELECTION:  -> SUI/EUR
2025-07-11 00:02:13,741 - [BITVAVO] - root - ERROR - [DEBUG] NO TIE - Single winner: SUI/EUR
2025-07-11 00:02:13,766 - [BITVAVO] - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-07-11 00:02:13,766 - [BITVAVO] - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 00:02:13,766 - [BITVAVO] - root - INFO - Single asset strategy with best asset: SUI/EUR
2025-07-11 00:02:13,766 - [BITVAVO] - root - INFO - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-07-11 00:02:13,766 - [BITVAVO] - root - INFO - [DEBUG]   - Best asset selected: SUI/EUR
2025-07-11 00:02:13,766 - [BITVAVO] - root - INFO - [DEBUG]   - Assets held: {'SUI/EUR': 1.0}
2025-07-11 00:02:13,766 - [BITVAVO] - root - INFO - [DEBUG]   - MTPI signal: 1
2025-07-11 00:02:13,766 - [BITVAVO] - root - INFO - [DEBUG]   - Use MTPI signal: True
2025-07-11 00:02:13,767 - [BITVAVO] - root - ERROR - 🚨 ? SUI/EUR WAS SELECTED
2025-07-11 00:02:13,866 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-11 00:02:13,868 - [BITVAVO] - root - INFO - Sent MTPI signal change notification: -1 -> 1
2025-07-11 00:02:13,869 - [BITVAVO] - root - ERROR - Missing data for template asset_rotation: 'previous_asset'
2025-07-11 00:02:13,869 - [BITVAVO] - root - INFO - Executing single-asset strategy with best asset: SUI/EUR
2025-07-11 00:02:13,869 - [BITVAVO] - root - INFO - Executing strategy signal: best_asset=SUI/EUR, mtpi_signal=1, mode=live
2025-07-11 00:02:14,044 - [BITVAVO] - root - INFO - Incremented daily trade counter for SUI/EUR: 1/5
2025-07-11 00:02:14,044 - [BITVAVO] - root - INFO - ASSET SELECTION - Attempting to enter position for selected asset: SUI/EUR
2025-07-11 00:02:14,045 - [BITVAVO] - root - INFO - Attempting to enter position for SUI/EUR in live mode
2025-07-11 00:02:14,045 - [BITVAVO] - root - INFO - [DEBUG] TRADE - SUI/EUR: Starting enter_position attempt
2025-07-11 00:02:14,045 - [BITVAVO] - root - INFO - [DEBUG] TRADE - SUI/EUR: Trading mode: live
2025-07-11 00:02:14,045 - [BITVAVO] - root - INFO - TRADE ATTEMPT - SUI/EUR: Getting current market price...
2025-07-11 00:02:14,046 - [BITVAVO] - root - INFO - [DEBUG] PRICE - SUI/EUR: Starting get_current_price
2025-07-11 00:02:14,046 - [BITVAVO] - root - INFO - [DEBUG] PRICE - SUI/EUR: Exchange ID: bitvavo
2025-07-11 00:02:14,046 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Using existing exchange instance
2025-07-11 00:02:14,046 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Symbol found in exchange markets
2025-07-11 00:02:14,046 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Attempting to fetch ticker...
2025-07-11 00:02:14,090 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-11 00:02:14,140 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker fetched successfully
2025-07-11 00:02:14,140 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker data: {'symbol': 'SUI/EUR', 'timestamp': 1752192127315, 'datetime': '2025-07-11T00:02:07.315Z', 'high': 2.9824, 'low': 2.6064, 'bid': 2.9772, 'bidVolume': 229.79928835, 'ask': 2.9794, 'askVolume': 825.626, 'vwap': 2.829505016335606, 'open': 2.6256, 'close': 2.9756, 'last': 2.9756, 'previousClose': None, 'change': 0.35, 'percentage': 13.330286410725167, 'average': 2.8006, 'baseVolume': 2828891.06015295, 'quoteVolume': 8004361.445369723, 'info': {'market': 'SUI-EUR', 'startTimestamp': '1752105727315', 'timestamp': '1752192127315', 'open': '2.6256', 'openTimestamp': '1752105819447', 'high': '2.9824', 'low': '2.6064', 'last': '2.9756', 'closeTimestamp': '1752192108039', 'bid': '2.977200', 'bidSize': '229.79928835', 'ask': '2.979400', 'askSize': '825.62600000', 'volume': '2828891.06015295', 'volumeQuote': '8004361.445369722855'}, 'indexPrice': None, 'markPrice': None}
2025-07-11 00:02:14,140 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Last price: 2.9756
2025-07-11 00:02:14,140 - [BITVAVO] - root - INFO - [DEBUG] TRADE - SUI/EUR: get_current_price returned: 2.9756
2025-07-11 00:02:14,140 - [BITVAVO] - root - INFO - [DEBUG] TRADE - SUI/EUR: Price type: <class 'float'>
2025-07-11 00:02:14,141 - [BITVAVO] - root - INFO - [DEBUG] TRADE - SUI/EUR: Price evaluation - not price: False
2025-07-11 00:02:14,141 - [BITVAVO] - root - INFO - [DEBUG] TRADE - SUI/EUR: Price evaluation - price <= 0: False
2025-07-11 00:02:14,141 - [BITVAVO] - root - INFO - TRADE ATTEMPT - SUI/EUR: Current price: 2.********
2025-07-11 00:02:14,366 - [BITVAVO] - root - INFO - Available balance for EUR: 4544.********
2025-07-11 00:02:14,367 - [BITVAVO] - root - INFO - Reserved 17.041371 USDC for fees (rate: 0.25% with buffer)
2025-07-11 00:02:14,372 - [BITVAVO] - root - INFO - Loaded market info for 176 trading pairs
2025-07-11 00:02:14,372 - [BITVAVO] - root - INFO - Calculated position size for SUI/EUR: 1513.******** (using 99.99% of 4544.82, accounting for fees)
2025-07-11 00:02:14,372 - [BITVAVO] - root - INFO - Calculated position size: 1513.******** SUI
2025-07-11 00:02:14,373 - [BITVAVO] - root - INFO - Entering position for SUI/EUR: 1513.******** units at 2.******** (value: 4504.******** EUR)
2025-07-11 00:02:14,373 - [BITVAVO] - root - INFO - Executing live market buy order for SUI/EUR
2025-07-11 00:02:14,461 - [BITVAVO] - root - INFO - Adjusted base amount for SUI/EUR: 1513.******** -> 1506.********
2025-07-11 00:02:14,542 - [BITVAVO] - root - ERROR - Error creating market buy order: bitvavo {"errorCode":203,"error":"operatorId parameter is required."}
2025-07-11 00:02:14,542 - [BITVAVO] - root - ERROR - TRADE FAILURE - SUI/EUR: Failed to create order for SUI/EUR
2025-07-11 00:02:14,552 - [BITVAVO] - root - ERROR - Trade failed: BUY SUI/EUR, amount=1513.********, price=2.********, reason=Failed to create order for SUI/EUR
2025-07-11 00:02:14,558 - [BITVAVO] - root - ERROR - Trade failed: UNKNOWN SUI/EUR, amount=0.00000000, price=0.00000000, reason=Failed to create order for SUI/EUR
2025-07-11 00:02:14,558 - [BITVAVO] - root - ERROR - TRADE FAILURE - SUI/EUR: Failed to enter position
2025-07-11 00:02:14,558 - [BITVAVO] - root - ERROR - TRADE FAILURE - SUI/EUR: Error type: order_creation_failed
2025-07-11 00:02:14,558 - [BITVAVO] - root - ERROR - TRADE FAILURE - SUI/EUR: Error reason: Failed to create order for SUI/EUR
2025-07-11 00:02:14,559 - [BITVAVO] - root - WARNING - HIGH-PRIORITY ASSET REJECTED: SUI/EUR - Failed to create order for SUI/EUR
2025-07-11 00:02:14,564 - [BITVAVO] - root - ERROR - Trade failed: UNKNOWN SUI/EUR, amount=0.00000000, price=0.00000000, reason=Failed to create order for SUI/EUR
2025-07-11 00:02:14,564 - [BITVAVO] - root - INFO - Single-asset trade result logged to trade log file
2025-07-11 00:02:14,564 - [BITVAVO] - root - ERROR - Trade execution failed: {'success': False, 'reason': 'Failed to create order for SUI/EUR', 'symbol': 'SUI/EUR', 'error_type': 'order_creation_failed'}
2025-07-11 00:02:14,580 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 400 Bad Request"
2025-07-11 00:02:14,580 - [BITVAVO] - root - WARNING - Failed to send with Markdown formatting: Can't parse entities: can't find end of the entity starting at byte offset 92
2025-07-11 00:02:14,615 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-11 00:02:14,621 - [BITVAVO] - root - INFO - Asset selection logged: 1 assets selected with single_asset allocation
2025-07-11 00:02:14,621 - [BITVAVO] - root - INFO - Asset scores (sorted by score):
2025-07-11 00:02:14,621 - [BITVAVO] - root - INFO -   SUI/EUR: score=11.0, status=SELECTED, weight=1.00
2025-07-11 00:02:14,621 - [BITVAVO] - root - INFO -   XRP/EUR: score=10.0, status=NOT SELECTED, weight=0.00
2025-07-11 00:02:14,621 - [BITVAVO] - root - INFO -   AAVE/EUR: score=10.0, status=NOT SELECTED, weight=0.00
2025-07-11 00:02:14,621 - [BITVAVO] - root - INFO -   ETH/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-07-11 00:02:14,621 - [BITVAVO] - root - INFO -   PEPE/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-07-11 00:02:14,621 - [BITVAVO] - root - INFO -   LINK/EUR: score=8.0, status=NOT SELECTED, weight=0.00
2025-07-11 00:02:14,621 - [BITVAVO] - root - INFO -   DOGE/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-07-11 00:02:14,621 - [BITVAVO] - root - INFO -   SOL/EUR: score=4.0, status=NOT SELECTED, weight=0.00
2025-07-11 00:02:14,622 - [BITVAVO] - root - INFO -   BTC/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-07-11 00:02:14,622 - [BITVAVO] - root - INFO -   ADA/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-07-11 00:02:14,622 - [BITVAVO] - root - INFO -   AVAX/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-07-11 00:02:14,622 - [BITVAVO] - root - INFO -   TRX/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-07-11 00:02:14,622 - [BITVAVO] - root - INFO -   BNB/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-07-11 00:02:14,622 - [BITVAVO] - root - INFO -   DOT/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-07-11 00:02:14,622 - [BITVAVO] - root - INFO - Asset selection logged with 14 assets scored and 1 assets selected
2025-07-11 00:02:14,622 - [BITVAVO] - root - INFO - Extracted asset scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 4.0, 'SUI/EUR': 11.0, 'XRP/EUR': 10.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 1.0, 'ADA/EUR': 3.0, 'LINK/EUR': 8.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 6.0, 'BNB/EUR': 0.0, 'DOT/EUR': 0.0}
2025-07-11 00:02:14,654 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-11 00:02:14,657 - [BITVAVO] - root - INFO - Strategy execution completed successfully in 45.34 seconds
2025-07-11 00:02:14,662 - [BITVAVO] - root - INFO - Saved recovery state to data/state/recovery_state.json