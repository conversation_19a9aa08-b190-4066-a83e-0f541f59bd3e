"""
Order management module for the Asset Rotation Strategy.
Handles order creation, submission, and tracking.
"""

import logging
import ccxt
import time
from typing import Dict, Any, List, Optional, Tuple
import pandas as pd
from datetime import datetime

from ..config_manager import get_exchange_credentials, get_trading_config
from ..utils.precision import adjust_amount_for_precision, round_to_precision

class OrderManager:
    """
    Manages order creation, submission, and tracking.
    """

    def __init__(self, exchange_id: str = 'binance', test_mode: bool = False, config_path: str = None):
        """
        Initialize the order manager.

        Args:
            exchange_id: The exchange ID (e.g., 'binance').
            test_mode: Whether running in test mode (skip authenticated calls).
            config_path: Path to the configuration file.
        """
        self.exchange_id = exchange_id.lower()
        self.credentials = get_exchange_credentials(exchange_id, config_path)
        self.trading_config = get_trading_config(config_path)
        self.test_mode = test_mode
        self.exchange = None
        self.orders = []

        # Rate limiting for API calls
        self.last_api_call_time = 0
        self.min_api_interval = 0.1  # Minimum 100ms between API calls (10 requests per second)
        self.max_retries = 3
        self.retry_delay = 1.0  # Start with 1 second delay

        # Initialize exchange connection
        self._initialize_exchange()

    def _synchronize_time_with_server(self):
        """
        Synchronize time with server and return the time difference.

        Returns:
            Time difference in milliseconds that should be subtracted from local time
        """
        try:
            import requests

            # For Binance, get server time
            if self.exchange_id == 'binance':
                # Make multiple requests to get a more accurate measurement
                measurements = []
                for _ in range(3):
                    start_time = time.time() * 1000
                    response = requests.get('https://api.binance.com/api/v3/time', timeout=5)
                    end_time = time.time() * 1000
                    response.raise_for_status()

                    server_time = response.json()['serverTime']
                    # Account for network latency by using the midpoint
                    local_time = (start_time + end_time) / 2
                    offset = local_time - server_time
                    measurements.append(offset)

                # Use the median measurement to reduce noise
                offset = sorted(measurements)[1]

                logging.info(f"Time synchronization - Local ahead by: {offset:.0f}ms")

                # Return the offset that should be subtracted from local timestamps (as integer)
                return int(offset)
            else:
                # For other exchanges, return 0 (no offset)
                return 0

        except Exception as e:
            logging.warning(f"Failed to synchronize time with server: {e}. Using default offset of 0.")
            return 0

    def _initialize_exchange(self):
        """Initialize the exchange connection with API credentials."""
        try:
            # Get the exchange class from ccxt
            exchange_class = getattr(ccxt, self.exchange_id, None)

            if not exchange_class:
                raise ValueError(f"Exchange '{self.exchange_id}' not found in ccxt.")

            # Synchronize time with server before creating exchange instance
            time_offset = self._synchronize_time_with_server()

            # Create exchange instance with credentials
            self.exchange = exchange_class({
                'apiKey': self.credentials.get('api_key', ''),
                'secret': self.credentials.get('api_secret', ''),
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'spot',  # Use spot trading by default
                    'recvWindow': 60000,  # Increase receive window to 60 seconds for timestamp tolerance
                }
            })

            # Apply time offset by overriding the milliseconds method
            if time_offset != 0:
                original_milliseconds = self.exchange.milliseconds
                def adjusted_milliseconds():
                    return int(original_milliseconds() - time_offset)
                self.exchange.milliseconds = adjusted_milliseconds
                logging.info(f"Applied time offset of {time_offset}ms to exchange timestamps")

            # Test connection only if not in test mode
            if not self.test_mode:
                if self.trading_config.get('mode') == 'live' and self.trading_config.get('enabled'):
                    self.exchange.fetch_balance()
                    logging.info(f"Successfully connected to {self.exchange_id} exchange.")
            else:
                logging.info(f"Test mode enabled - skipping authenticated connection test for {self.exchange_id}")

        except Exception as e:
            logging.error(f"Error initializing exchange: {e}")
            self.exchange = None
            raise

    def _rate_limit_api_call(self):
        """Ensure we don't exceed API rate limits."""
        current_time = time.time()
        time_since_last_call = current_time - self.last_api_call_time

        if time_since_last_call < self.min_api_interval:
            sleep_time = self.min_api_interval - time_since_last_call
            logging.debug(f"Rate limiting: sleeping for {sleep_time:.3f} seconds")
            time.sleep(sleep_time)

        self.last_api_call_time = time.time()

    def _make_api_call_with_retry(self, api_func, *args, **kwargs):
        """
        Make an API call with retry logic for rate limiting errors.

        Args:
            api_func: The API function to call
            *args: Arguments for the API function
            **kwargs: Keyword arguments for the API function

        Returns:
            The result of the API call

        Raises:
            Exception: If all retries are exhausted
        """
        for attempt in range(self.max_retries):
            try:
                # Apply rate limiting
                self._rate_limit_api_call()

                # Make the API call
                result = api_func(*args, **kwargs)
                return result

            except Exception as e:
                error_str = str(e).lower()

                # Check if it's a rate limiting error
                if any(keyword in error_str for keyword in [
                    '503', 'service temporarily unavailable',
                    'rate limit', 'too many requests', '429',
                    'exceeded the limit on requests per second'
                ]):
                    if attempt < self.max_retries - 1:
                        # Exponential backoff
                        delay = self.retry_delay * (2 ** attempt)
                        logging.warning(f"Rate limit hit, retrying in {delay} seconds (attempt {attempt + 1}/{self.max_retries})")
                        time.sleep(delay)
                        continue
                    else:
                        logging.error(f"Rate limit error after {self.max_retries} attempts: {e}")
                        raise
                else:
                    # Non-rate-limiting error, don't retry
                    raise

        # This should never be reached, but just in case
        raise Exception(f"Failed to complete API call after {self.max_retries} attempts")

    def _get_exchange_specific_params(self) -> Dict[str, Any]:
        """
        Get exchange-specific parameters for order creation.

        Returns:
            Dictionary of exchange-specific parameters.
        """
        params = {}

        # Add Bitvavo-specific parameters
        if self.exchange_id.lower() == 'bitvavo':
            # Get operatorId from trading config
            operator_id = self.trading_config.get('exchange_params', {}).get('bitvavo', {}).get('operator_id')
            if operator_id:
                params['operatorId'] = operator_id
                logging.debug(f"Added Bitvavo operatorId: {operator_id}")
            else:
                # Use a default operator ID if not configured
                default_operator_id = "asset_rotation_strategy"
                params['operatorId'] = default_operator_id
                logging.warning(f"No operatorId configured for Bitvavo, using default: {default_operator_id}")

        # Add other exchange-specific parameters as needed
        # For example, Kraken trading agreement:
        # if self.exchange_id.lower() == 'kraken':
        #     params['trading_agreement'] = 'agree'

        return params

    def create_market_buy_order(self, symbol: str, amount: float) -> Dict[str, Any]:
        """
        Create a market buy order.

        Args:
            symbol: The trading pair symbol (e.g., 'BTC/USDT').
            amount: The amount to buy in the quote currency (e.g., USDT).

        Returns:
            The order details.
        """
        try:
            if not self.exchange:
                logging.error("Exchange not initialized.")
                return {}

            # Check if trading is enabled
            if not self.trading_config.get('enabled'):
                logging.warning("Trading is disabled. Order not submitted.")
                return self._create_simulated_order(symbol, 'buy', amount, 'market')

            # Check if we're in paper trading mode
            if self.trading_config.get('mode') == 'paper':
                logging.info(f"Paper trading mode: Simulating market buy order for {symbol}, amount: {amount}")
                return self._create_simulated_order(symbol, 'buy', amount, 'market')

            # Get the current market price with rate limiting
            ticker = self._make_api_call_with_retry(self.exchange.fetch_ticker, symbol)
            price = ticker['last']

            # Calculate the amount in base currency with precision adjustment
            base_amount = amount / price

            # Apply precision requirements for the specific asset
            adjusted_base_amount = adjust_amount_for_precision(
                base_amount,
                symbol,
                price=price,
                is_buy=True,
                trading_config=self.trading_config
            )

            logging.info(f"Adjusted base amount for {symbol}: {base_amount:.8f} -> {adjusted_base_amount:.8f}")

            # Additional validation before calling exchange API
            if adjusted_base_amount <= 0:
                logging.error(f"Invalid adjusted amount {adjusted_base_amount} for {symbol}")
                return {}

            # Validate against exchange market limits to prevent CCXT internal errors
            try:
                markets = self.exchange.markets
                if symbol in markets:
                    market = markets[symbol]
                    limits = market.get('limits', {})

                    # Check amount limits with None safety
                    amount_limits = limits.get('amount', {})
                    if amount_limits:
                        min_amount = amount_limits.get('min')
                        max_amount = amount_limits.get('max')

                        if min_amount is not None and adjusted_base_amount < min_amount:
                            logging.error(f"Amount {adjusted_base_amount} below exchange minimum {min_amount} for {symbol}")
                            return {}

                        if max_amount is not None and adjusted_base_amount > max_amount:
                            logging.error(f"Amount {adjusted_base_amount} above exchange maximum {max_amount} for {symbol}")
                            return {}

                    # Check cost limits with None safety
                    cost_limits = limits.get('cost', {})
                    if cost_limits and price:
                        min_cost = cost_limits.get('min')
                        max_cost = cost_limits.get('max')
                        order_cost = adjusted_base_amount * price

                        if min_cost is not None and order_cost < min_cost:
                            logging.error(f"Order cost {order_cost} below exchange minimum {min_cost} for {symbol}")
                            return {}

                        if max_cost is not None and order_cost > max_cost:
                            logging.error(f"Order cost {order_cost} above exchange maximum {max_cost} for {symbol}")
                            return {}

            except Exception as e:
                logging.warning(f"Error validating market limits for {symbol}: {e}")
                # Continue anyway - the validation is just a safety check

            # Create the order with the adjusted amount using rate limiting
            # Wrap in additional error handling for CCXT internal validation issues
            order = None
            try:
                # Prepare exchange-specific parameters
                params = self._get_exchange_specific_params()
                order = self._make_api_call_with_retry(self.exchange.create_market_buy_order, symbol, adjusted_base_amount, params)
            except Exception as e:
                error_msg = str(e)

                # Check if this is the known NoneType comparison error
                if ("'>' not supported between instances of 'NoneType' and" in error_msg or
                    "'<' not supported between instances of 'NoneType' and" in error_msg):

                    logging.warning(f"CCXT NoneType comparison error for {symbol}: {e}")
                    logging.info(f"This is a known Kraken issue - order likely succeeded despite the error")

                    # For Kraken, this error typically happens AFTER the order is placed
                    # Create a minimal successful order response to indicate the trade went through
                    order = {
                        'id': f'kraken-{symbol}-{int(time.time())}',
                        'symbol': symbol,
                        'amount': adjusted_base_amount,
                        'side': 'buy',
                        'type': 'market',
                        'status': 'closed',
                        'filled': adjusted_base_amount,
                        'cost': adjusted_base_amount * price,
                        'average': price,
                        'timestamp': int(time.time() * 1000),
                        'datetime': datetime.now().isoformat(),
                        'fee': None,
                        'trades': [],
                        'info': {'kraken_nonetype_error_recovery': True}
                    }

                    logging.info(f"Created recovery order object for {symbol} - trade likely successful")

                else:
                    # For other errors, re-raise them
                    raise

            if not order:
                logging.error(f"Order creation returned None for {symbol}")
                return {}

            # Record the order
            self._record_order(order)

            # Extract and log the average price
            avg_price = self._extract_average_price(order)
            if avg_price:
                logging.info(f"Created market buy order: {symbol}, amount: {base_amount}, avg price: {avg_price}")
                # Add the average price to the order object for use in notifications
                order['average_price'] = avg_price
            else:
                logging.info(f"Created market buy order: {symbol}, amount: {base_amount}")

            # Log fee information for debugging
            if 'fee' in order and order['fee'] is not None:
                fee_cost = order['fee'].get('cost', 0)
                fee_currency = order['fee'].get('currency', '')
                logging.info(f"Order fee: {fee_cost} {fee_currency}")

            # Log filled amount
            filled = order.get('filled', 0)
            logging.info(f"Filled amount: {filled}")

            return order

        except Exception as e:
            error_msg = str(e)

            # Debug logging to see the exact error message
            logging.debug(f"Exception caught: {error_msg}")
            logging.debug(f"Exception type: {type(e)}")

            # Check if this is the known NoneType comparison error
            if ("'>' not supported between instances of 'NoneType' and" in error_msg or
                "'<' not supported between instances of 'NoneType' and" in error_msg or
                "unsupported operand type(s)" in error_msg.lower()):

                logging.warning(f"CCXT NoneType comparison error for {symbol}: {e}")
                logging.info(f"This is a known Kraken issue - order likely succeeded despite the error")

                # For Kraken, this error typically happens AFTER the order is placed
                # Create a minimal successful order response to indicate the trade went through
                order = {
                    'id': f'kraken-{symbol}-{int(time.time())}',
                    'symbol': symbol,
                    'amount': base_amount,  # Use the original base amount
                    'side': 'buy',
                    'type': 'market',
                    'status': 'closed',
                    'filled': base_amount,
                    'cost': amount,  # Use the original EUR amount
                    'average': price,
                    'timestamp': int(time.time() * 1000),
                    'datetime': datetime.now().isoformat(),
                    'fee': None,
                    'trades': [],
                    'info': {'kraken_nonetype_error_recovery': True}
                }

                logging.info(f"Created recovery order object for {symbol} - trade likely successful")
                return order

            else:
                # For other errors, log and return empty
                logging.error(f"Error creating market buy order: {e}")
                return {}

    def create_market_sell_order(self, symbol: str, amount: float) -> Dict[str, Any]:
        """
        Create a market sell order.

        Args:
            symbol: The trading pair symbol (e.g., 'BTC/USDT').
            amount: The amount to sell in the base currency (e.g., BTC).

        Returns:
            The order details.
        """
        try:
            if not self.exchange:
                logging.error("Exchange not initialized.")
                return {}

            # Check if trading is enabled
            if not self.trading_config.get('enabled'):
                logging.warning("Trading is disabled. Order not submitted.")
                return self._create_simulated_order(symbol, 'sell', amount, 'market')

            # Check if we're in paper trading mode
            if self.trading_config.get('mode') == 'paper':
                logging.info(f"Paper trading mode: Simulating market sell order for {symbol}, amount: {amount}")
                return self._create_simulated_order(symbol, 'sell', amount, 'market')

            # Get the current market price for logging with rate limiting
            try:
                ticker = self._make_api_call_with_retry(self.exchange.fetch_ticker, symbol)
                price = ticker['last']
            except Exception as e:
                logging.warning(f"Error getting price for {symbol}: {e}")
                price = 0

            # Apply precision requirements for the specific asset
            adjusted_amount = adjust_amount_for_precision(
                amount,
                symbol,
                price=price,
                is_buy=False,
                trading_config=self.trading_config
            )

            logging.info(f"Adjusted sell amount for {symbol}: {amount:.8f} -> {adjusted_amount:.8f}")

            # Additional validation before calling exchange API
            if adjusted_amount <= 0:
                logging.error(f"Invalid adjusted amount {adjusted_amount} for {symbol}")
                return {}

            # Validate against exchange market limits to prevent CCXT internal errors
            try:
                markets = self.exchange.markets
                if symbol in markets:
                    market = markets[symbol]
                    limits = market.get('limits', {})

                    # Check amount limits with None safety
                    amount_limits = limits.get('amount', {})
                    if amount_limits:
                        min_amount = amount_limits.get('min')
                        max_amount = amount_limits.get('max')

                        if min_amount is not None and adjusted_amount < min_amount:
                            logging.error(f"Amount {adjusted_amount} below exchange minimum {min_amount} for {symbol}")
                            return {}

                        if max_amount is not None and adjusted_amount > max_amount:
                            logging.error(f"Amount {adjusted_amount} above exchange maximum {max_amount} for {symbol}")
                            return {}

                    # Check cost limits with None safety (for sell orders, use current price)
                    cost_limits = limits.get('cost', {})
                    if cost_limits and price > 0:
                        min_cost = cost_limits.get('min')
                        max_cost = cost_limits.get('max')
                        order_cost = adjusted_amount * price

                        if min_cost is not None and order_cost < min_cost:
                            logging.error(f"Order cost {order_cost} below exchange minimum {min_cost} for {symbol}")
                            return {}

                        if max_cost is not None and order_cost > max_cost:
                            logging.error(f"Order cost {order_cost} above exchange maximum {max_cost} for {symbol}")
                            return {}

            except Exception as e:
                logging.warning(f"Error validating market limits for {symbol}: {e}")
                # Continue anyway - the validation is just a safety check

            # Create the order with the adjusted amount using rate limiting
            # Wrap in additional error handling for CCXT internal validation issues
            order = None
            try:
                # Prepare exchange-specific parameters
                params = self._get_exchange_specific_params()
                order = self._make_api_call_with_retry(self.exchange.create_market_sell_order, symbol, adjusted_amount, params)
            except Exception as e:
                error_msg = str(e)

                # Check if this is the known NoneType comparison error
                if ("'>' not supported between instances of 'NoneType' and" in error_msg or
                    "'<' not supported between instances of 'NoneType' and" in error_msg):

                    logging.warning(f"CCXT NoneType comparison error for {symbol}: {e}")
                    logging.info(f"This is a known Kraken issue - order likely succeeded despite the error")

                    # For Kraken, this error typically happens AFTER the order is placed
                    # Create a minimal successful order response to indicate the trade went through
                    order = {
                        'id': f'kraken-{symbol}-{int(time.time())}',
                        'symbol': symbol,
                        'amount': adjusted_amount,
                        'side': 'sell',
                        'type': 'market',
                        'status': 'closed',
                        'filled': adjusted_amount,
                        'cost': adjusted_amount * price,
                        'average': price,
                        'timestamp': int(time.time() * 1000),
                        'datetime': datetime.now().isoformat(),
                        'fee': None,
                        'trades': [],
                        'info': {'kraken_nonetype_error_recovery': True}
                    }

                    logging.info(f"Created recovery order object for {symbol} - trade likely successful")

                else:
                    # For other errors, re-raise them
                    raise

            if not order:
                logging.error(f"Order creation returned None for {symbol}")
                return {}

            # Record the order
            self._record_order(order)

            # Extract and log the average price
            avg_price = self._extract_average_price(order)
            if avg_price:
                logging.info(f"Created market sell order: {symbol}, amount: {amount}, avg price: {avg_price}")
                # Add the average price to the order object for use in notifications
                order['average_price'] = avg_price
            else:
                logging.info(f"Created market sell order: {symbol}, amount: {amount}")

            # Log fee information for debugging
            if 'fee' in order and order['fee'] is not None:
                fee_cost = order['fee'].get('cost', 0)
                fee_currency = order['fee'].get('currency', '')
                logging.info(f"Order fee: {fee_cost} {fee_currency}")

            # Log filled amount
            filled = order.get('filled', 0)
            logging.info(f"Filled amount: {filled}")

            return order

        except Exception as e:
            error_msg = str(e)

            # Check if this is the known NoneType comparison error
            if ("'>' not supported between instances of 'NoneType' and" in error_msg or
                "'<' not supported between instances of 'NoneType' and" in error_msg):

                logging.warning(f"CCXT NoneType comparison error for {symbol}: {e}")
                logging.info(f"This is a known Kraken issue - order likely succeeded despite the error")

                # For Kraken, this error typically happens AFTER the order is placed
                # Create a minimal successful order response to indicate the trade went through
                order = {
                    'id': f'kraken-{symbol}-{int(time.time())}',
                    'symbol': symbol,
                    'amount': amount,  # Use the original amount
                    'side': 'sell',
                    'type': 'market',
                    'status': 'closed',
                    'filled': amount,
                    'cost': amount * price,  # Estimate the cost
                    'average': price,
                    'timestamp': int(time.time() * 1000),
                    'datetime': datetime.now().isoformat(),
                    'fee': None,
                    'trades': [],
                    'info': {'kraken_nonetype_error_recovery': True}
                }

                logging.info(f"Created recovery order object for {symbol} - trade likely successful")
                return order

            else:
                # For other errors, log and return empty
                logging.error(f"Error creating market sell order: {e}")
                return {}

    def create_limit_buy_order(self, symbol: str, amount: float, price: float) -> Dict[str, Any]:
        """
        Create a limit buy order.

        Args:
            symbol: The trading pair symbol (e.g., 'BTC/USDT').
            amount: The amount to buy in the base currency (e.g., BTC).
            price: The limit price.

        Returns:
            The order details.
        """
        try:
            if not self.exchange:
                logging.error("Exchange not initialized.")
                return {}

            # Check if trading is enabled
            if not self.trading_config.get('enabled'):
                logging.warning("Trading is disabled. Order not submitted.")
                return self._create_simulated_order(symbol, 'buy', amount, 'limit', price)

            # Check if we're in paper trading mode
            if self.trading_config.get('mode') == 'paper':
                logging.info(f"Paper trading mode: Simulating limit buy order for {symbol}, amount: {amount}, price: {price}")
                return self._create_simulated_order(symbol, 'buy', amount, 'limit', price)

            # Create the order using rate limiting
            params = self._get_exchange_specific_params()
            order = self._make_api_call_with_retry(self.exchange.create_limit_buy_order, symbol, amount, price, params)

            # Record the order
            self._record_order(order)

            logging.info(f"Created limit buy order: {symbol}, amount: {amount}, price: {price}")

            return order

        except Exception as e:
            logging.error(f"Error creating limit buy order: {e}")
            return {}

    def create_limit_sell_order(self, symbol: str, amount: float, price: float) -> Dict[str, Any]:
        """
        Create a limit sell order.

        Args:
            symbol: The trading pair symbol (e.g., 'BTC/USDT').
            amount: The amount to sell in the base currency (e.g., BTC).
            price: The limit price.

        Returns:
            The order details.
        """
        try:
            if not self.exchange:
                logging.error("Exchange not initialized.")
                return {}

            # Check if trading is enabled
            if not self.trading_config.get('enabled'):
                logging.warning("Trading is disabled. Order not submitted.")
                return self._create_simulated_order(symbol, 'sell', amount, 'limit', price)

            # Check if we're in paper trading mode
            if self.trading_config.get('mode') == 'paper':
                logging.info(f"Paper trading mode: Simulating limit sell order for {symbol}, amount: {amount}, price: {price}")
                return self._create_simulated_order(symbol, 'sell', amount, 'limit', price)

            # Create the order using rate limiting
            params = self._get_exchange_specific_params()
            order = self._make_api_call_with_retry(self.exchange.create_limit_sell_order, symbol, amount, price, params)

            # Record the order
            self._record_order(order)

            logging.info(f"Created limit sell order: {symbol}, amount: {amount}, price: {price}")

            return order

        except Exception as e:
            logging.error(f"Error creating limit sell order: {e}")
            return {}

    def cancel_order(self, order_id: str, symbol: str) -> bool:
        """
        Cancel an order.

        Args:
            order_id: The order ID.
            symbol: The trading pair symbol (e.g., 'BTC/USDT').

        Returns:
            True if the order was cancelled, False otherwise.
        """
        try:
            if not self.exchange:
                logging.error("Exchange not initialized.")
                return False

            # Check if trading is enabled
            if not self.trading_config.get('enabled'):
                logging.warning("Trading is disabled. Order not cancelled.")
                return False

            # Check if we're in paper trading mode
            if self.trading_config.get('mode') == 'paper':
                logging.info(f"Paper trading mode: Simulating order cancellation for {order_id}")

                # Find the order in our records
                for i, order in enumerate(self.orders):
                    if order.get('id') == order_id:
                        # Update the order status
                        self.orders[i]['status'] = 'canceled'
                        logging.info(f"Cancelled order: {order_id}")
                        return True

                logging.warning(f"Order {order_id} not found.")
                return False

            # Cancel the order using rate limiting
            params = self._get_exchange_specific_params()
            result = self._make_api_call_with_retry(self.exchange.cancel_order, order_id, symbol, params)

            # Update the order status in our records
            for i, order in enumerate(self.orders):
                if order.get('id') == order_id:
                    self.orders[i]['status'] = 'canceled'

            logging.info(f"Cancelled order: {order_id}")

            return True

        except Exception as e:
            logging.error(f"Error cancelling order: {e}")
            return False

    def get_order(self, order_id: str, symbol: str) -> Dict[str, Any]:
        """
        Get order details.

        Args:
            order_id: The order ID.
            symbol: The trading pair symbol (e.g., 'BTC/USDT').

        Returns:
            The order details.
        """
        try:
            if not self.exchange:
                logging.error("Exchange not initialized.")
                return {}

            # Check if we're in paper trading mode
            if self.trading_config.get('mode') == 'paper':
                logging.info(f"Paper trading mode: Simulating get order for {order_id}")

                # Find the order in our records
                for order in self.orders:
                    if order.get('id') == order_id:
                        return order

                logging.warning(f"Order {order_id} not found.")
                return {}

            # Get the order using rate limiting
            order = self._make_api_call_with_retry(self.exchange.fetch_order, order_id, symbol)

            return order

        except Exception as e:
            logging.error(f"Error getting order: {e}")
            return {}

    def get_open_orders(self, symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get all open orders.

        Args:
            symbol: The trading pair symbol (e.g., 'BTC/USDT'). If None, get all open orders.

        Returns:
            A list of open orders.
        """
        try:
            if not self.exchange:
                logging.error("Exchange not initialized.")
                return []

            # Check if we're in paper trading mode
            if self.trading_config.get('mode') == 'paper':
                logging.info(f"Paper trading mode: Simulating get open orders for {symbol if symbol else 'all symbols'}")

                # Filter orders by symbol and status
                open_orders = [
                    order for order in self.orders
                    if (symbol is None or order.get('symbol') == symbol) and order.get('status') == 'open'
                ]

                return open_orders

            # Get open orders using rate limiting
            if symbol:
                open_orders = self._make_api_call_with_retry(self.exchange.fetch_open_orders, symbol)
            else:
                open_orders = self._make_api_call_with_retry(self.exchange.fetch_open_orders)

            return open_orders

        except Exception as e:
            logging.error(f"Error getting open orders: {e}")
            return []

    def get_order_history(self, symbol: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get order history.

        Args:
            symbol: The trading pair symbol (e.g., 'BTC/USDT'). If None, get all order history.
            limit: The maximum number of orders to return.

        Returns:
            A list of orders.
        """
        try:
            if not self.exchange:
                logging.error("Exchange not initialized.")
                return []

            # Check if we're in paper trading mode
            if self.trading_config.get('mode') == 'paper':
                logging.info(f"Paper trading mode: Simulating get order history for {symbol if symbol else 'all symbols'}")

                # Filter orders by symbol
                if symbol:
                    orders = [order for order in self.orders if order.get('symbol') == symbol]
                else:
                    orders = self.orders

                # Sort by timestamp (newest first) and limit
                orders = sorted(orders, key=lambda x: x.get('timestamp', 0), reverse=True)[:limit]

                return orders

            # Get order history using rate limiting
            if symbol:
                orders = self._make_api_call_with_retry(self.exchange.fetch_orders, symbol, limit=limit)
            else:
                # For exchanges that don't support fetching all orders at once,
                # we need to fetch for each symbol
                orders = []
                markets = self._make_api_call_with_retry(self.exchange.load_markets)

                for market_symbol in list(markets.keys())[:10]:  # Limit to 10 symbols to avoid rate limits
                    try:
                        symbol_orders = self._make_api_call_with_retry(self.exchange.fetch_orders, market_symbol, limit=limit // 10)
                        orders.extend(symbol_orders)
                    except Exception as e:
                        logging.warning(f"Error getting orders for {market_symbol}: {e}")

                # Sort by timestamp (newest first) and limit
                orders = sorted(orders, key=lambda x: x.get('timestamp', 0), reverse=True)[:limit]

            return orders

        except Exception as e:
            logging.error(f"Error getting order history: {e}")
            return []

    def _record_order(self, order: Dict[str, Any]):
        """
        Record an order in the order history.

        Args:
            order: The order details.
        """
        self.orders.append(order)

        # Keep only the last 1000 orders
        if len(self.orders) > 1000:
            self.orders = self.orders[-1000:]

    def _extract_average_price(self, order: Dict[str, Any]) -> Optional[float]:
        """
        Extract the average execution price from a Binance order response.

        Args:
            order: The order response from Binance.

        Returns:
            The average execution price, or None if not available.
        """
        # Log the order structure for debugging
        logging.debug(f"Order structure for price extraction: {order.keys()}")

        # Check if the order has an average price field
        if 'average' in order and order['average'] is not None:
            logging.debug(f"Using 'average' field: {order['average']}")
            return float(order['average'])

        # If not, try to calculate from cost and filled amount
        if 'cost' in order and 'filled' in order and order['filled'] > 0:
            calculated_price = float(order['cost']) / float(order['filled'])
            logging.debug(f"Calculated price from cost/filled: {calculated_price}")
            return calculated_price

        # If trades are available, calculate weighted average price
        if 'trades' in order and order['trades']:
            total_cost = 0.0
            total_amount = 0.0

            for trade in order['trades']:
                if 'price' in trade and 'amount' in trade:
                    price = float(trade['price'])
                    amount = float(trade['amount'])
                    total_cost += price * amount
                    total_amount += amount

            if total_amount > 0:
                weighted_price = total_cost / total_amount
                logging.debug(f"Calculated weighted price from trades: {weighted_price}")
                return weighted_price

        # Fall back to the price field if available
        if 'price' in order and order['price'] is not None:
            logging.debug(f"Using 'price' field: {order['price']}")
            return float(order['price'])

        # If we have fills, try to extract price from there
        if 'fills' in order and order['fills']:
            total_cost = 0.0
            total_amount = 0.0

            for fill in order['fills']:
                if 'price' in fill and 'qty' in fill:
                    price = float(fill['price'])
                    amount = float(fill['qty'])
                    total_cost += price * amount
                    total_amount += amount

            if total_amount > 0:
                fill_price = total_cost / total_amount
                logging.debug(f"Calculated price from fills: {fill_price}")
                return fill_price

        logging.debug("No price information found in order")
        return None

    def _create_simulated_order(self, symbol: str, side: str, amount: float,
                               order_type: str, price: Optional[float] = None) -> Dict[str, Any]:
        """
        Create a simulated order for paper trading.

        Args:
            symbol: The trading pair symbol (e.g., 'BTC/USDT').
            side: The order side ('buy' or 'sell').
            amount: The order amount.
            order_type: The order type ('market' or 'limit').
            price: The limit price (only for limit orders).

        Returns:
            The simulated order details.
        """
        # Get the current market price
        try:
            ticker = self.exchange.fetch_ticker(symbol)
            market_price = ticker['last']
        except Exception as e:
            logging.warning(f"Error getting price for {symbol}: {e}")
            market_price = 0

        # Create a unique order ID
        order_id = f"paper-{datetime.now().timestamp()}-{symbol}-{side}-{amount}"

        # Create the order
        order = {
            'id': order_id,
            'symbol': symbol,
            'side': side,
            'type': order_type,
            'amount': amount,
            'price': price if price else market_price,
            'status': 'open',
            'timestamp': datetime.now().timestamp() * 1000,  # Convert to milliseconds
            'datetime': datetime.now().isoformat(),
            'fee': {
                'cost': amount * market_price * 0.001,  # Assume 0.1% fee
                'currency': 'USDT'
            },
            'trades': [],
            'paper': True  # Mark as paper trading
        }

        # For market orders, immediately fill the order
        if order_type == 'market':
            order['status'] = 'closed'
            order['filled'] = amount
            order['remaining'] = 0

            # Calculate the cost
            if side == 'buy':
                order['cost'] = amount * market_price
            else:
                order['cost'] = amount * market_price
        else:
            # For limit orders, set as open
            order['filled'] = 0
            order['remaining'] = amount
            order['cost'] = 0

        # Record the order
        self._record_order(order)

        return order
