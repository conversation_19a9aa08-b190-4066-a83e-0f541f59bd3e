2025-07-11 07:18:46,507 - [<PERSON><PERSON><PERSON><PERSON>] - root - INFO - Exchange-specific logging initialized for bitvavo
2025-07-11 07:18:46,880 - [B<PERSON>VA<PERSON>] - root - INFO - Telegram command handlers registered
2025-07-11 07:18:46,883 - [<PERSON><PERSON><PERSON><PERSON>] - root - ERROR - Error in Telegram polling: set_wakeup_fd only works in main thread of the main interpreter
2025-07-11 07:18:46,883 - [BITVAVO] - root - WARNING - Continuing without Telegram bot polling. Notifications will still be sent.
2025-07-11 07:18:46,883 - [BITVAVO] - root - INFO - Telegram bot polling started
2025-07-11 07:18:46,883 - [BITVAVO] - root - INFO - Telegram notifier initialized with notification level: standard
2025-07-11 07:18:46,883 - [B<PERSON><PERSON><PERSON>] - root - INFO - Telegram notification channel initialized
2025-07-11 07:18:46,884 - [<PERSON><PERSON><PERSON><PERSON>] - root - INFO - Successfully loaded templates using utf-8 encoding
2025-07-11 07:18:46,884 - [<PERSON><PERSON><PERSON><PERSON>] - root - INFO - <PERSON>aded 24 templates from file
2025-07-11 07:18:46,884 - [<PERSON><PERSON><PERSON><PERSON>] - root - INFO - Notification manager initialized with 1 channels
2025-07-11 07:18:46,884 - [BITVAVO] - root - INFO - Notification manager initialized
2025-07-11 07:18:46,884 - [BITVAVO] - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-07-11 07:18:46,885 - [BITVAVO] - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-07-11 07:18:46,885 - [BITVAVO] - root - INFO - Set up critical time windows for 1d timeframe
2025-07-11 07:18:46,885 - [BITVAVO] - root - INFO - Network watchdog initialized with 10s check interval
2025-07-11 07:18:46,889 - [BITVAVO] - root - INFO - Loaded recovery state from data/state/recovery_state.json
2025-07-11 07:18:46,891 - [BITVAVO] - root - INFO - No state file found at /home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/data/state/data/state/background_service_20250627_214259.json.json
2025-07-11 07:18:46,891 - [BITVAVO] - root - INFO - Recovery manager initialized
2025-07-11 07:18:46,891 - [BITVAVO] - root - INFO - [DEBUG] TRADING EXECUTOR - Initializing with exchange: bitvavo
2025-07-11 07:18:46,891 - [BITVAVO] - root - INFO - [DEBUG] TRADING EXECUTOR - Trading config: {'enabled': True, 'exchange': 'bitvavo', 'initial_capital': 100, 'max_slippage_pct': 0.5, 'min_order_values': {'default': 5.0}, 'mode': 'live', 'order_type': 'market', 'position_size_pct': 1, 'risk_management': {'max_daily_trades': 5, 'max_open_positions': 10}, 'transaction_fee_rate': 0.0025, 'exchange_params': {'bitvavo': {'operator_id': 'asset_rotation_strategy_v1'}}}
2025-07-11 07:18:46,891 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 07:18:46,917 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:18:46,918 - [BITVAVO] - root - INFO - Getting credentials for exchange_id: bitvavo
2025-07-11 07:18:46,920 - [BITVAVO] - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-07-11 07:18:46,920 - [BITVAVO] - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-07-11 07:18:46,920 - [BITVAVO] - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-07-11 07:18:46,921 - [BITVAVO] - root - INFO - Getting credentials for exchange_id: bitvavo
2025-07-11 07:18:46,921 - [BITVAVO] - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-07-11 07:18:46,921 - [BITVAVO] - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-07-11 07:18:46,921 - [BITVAVO] - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-07-11 07:18:46,921 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 07:18:46,936 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:18:47,492 - [BITVAVO] - root - INFO - Successfully loaded markets for bitvavo.
2025-07-11 07:18:47,568 - [BITVAVO] - root - INFO - Successfully connected to bitvavo exchange.
2025-07-11 07:18:47,568 - [BITVAVO] - root - INFO - Getting credentials for exchange_id: bitvavo
2025-07-11 07:18:47,568 - [BITVAVO] - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-07-11 07:18:47,568 - [BITVAVO] - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-07-11 07:18:47,568 - [BITVAVO] - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-07-11 07:18:47,568 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 07:18:47,580 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:18:48,129 - [BITVAVO] - root - INFO - Successfully connected to bitvavo exchange.
2025-07-11 07:18:48,130 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 07:18:48,154 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:18:48,154 - [BITVAVO] - root - INFO - Trading executor initialized for bitvavo
2025-07-11 07:18:48,155 - [BITVAVO] - root - INFO - Trading mode: live
2025-07-11 07:18:48,155 - [BITVAVO] - root - INFO - Trading enabled: True
2025-07-11 07:18:48,155 - [BITVAVO] - root - INFO - Getting credentials for exchange_id: bitvavo
2025-07-11 07:18:48,155 - [BITVAVO] - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-07-11 07:18:48,155 - [BITVAVO] - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-07-11 07:18:48,155 - [BITVAVO] - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-07-11 07:18:48,155 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 07:18:48,167 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:18:48,543 - [BITVAVO] - root - INFO - Successfully loaded markets for bitvavo.
2025-07-11 07:18:48,715 - [BITVAVO] - root - INFO - Successfully connected to bitvavo exchange.
2025-07-11 07:18:48,716 - [BITVAVO] - root - INFO - Trading enabled in live mode
2025-07-11 07:18:49,001 - [BITVAVO] - root - INFO - Connected to bitvavo, balance: 4544.82 EUR
2025-07-11 07:18:49,001 - [BITVAVO] - root - INFO - Generated run ID: 20250711_071849
2025-07-11 07:18:49,002 - [BITVAVO] - root - INFO - Ensured metrics directory exists: Performance_Metrics
2025-07-11 07:18:49,002 - [BITVAVO] - root - INFO - Background service initialized
2025-07-11 07:18:49,003 - [BITVAVO] - root - INFO - Network watchdog started
2025-07-11 07:18:49,004 - [BITVAVO] - root - INFO - Network watchdog started
2025-07-11 07:18:49,006 - [BITVAVO] - root - INFO - Schedule set up for 1d timeframe
2025-07-11 07:18:49,006 - [BITVAVO] - root - INFO - Background service started
2025-07-11 07:18:49,008 - [BITVAVO] - root - INFO - Executing strategy (run #1)...
2025-07-11 07:18:49,014 - [BITVAVO] - root - INFO - Resetting daily trade counters for this strategy execution
2025-07-11 07:18:49,015 - [BITVAVO] - root - INFO - No trades recorded today (Max: 5)
2025-07-11 07:18:49,018 - [BITVAVO] - root - INFO - Initialized daily trades counter for 2025-07-11
2025-07-11 07:18:49,024 - [BITVAVO] - root - INFO - Creating snapshot for candle timestamp: 20250711
2025-07-11 07:18:49,109 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-11 07:18:50,035 - [BITVAVO] - root - WARNING - Failed to send with Markdown formatting: Unknown error in HTTP implementation: RuntimeError('<asyncio.locks.Event object at 0x7a546bb63dd0 [unset]> is bound to a different event loop')
2025-07-11 07:18:50,112 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-11 07:18:50,115 - [BITVAVO] - root - INFO - Using trend method 'PGO For Loop' for strategy execution
2025-07-11 07:18:50,115 - [BITVAVO] - root - INFO - Using configured start date for 1d timeframe: 2025-02-10
2025-07-11 07:18:50,115 - [BITVAVO] - root - INFO - Using recent date for performance tracking: 2025-07-04
2025-07-11 07:18:50,117 - [BITVAVO] - root - INFO - Using real-time data fetching - only fetching new data since last cached timestamp
2025-07-11 07:18:50,207 - [BITVAVO] - root - INFO - Loaded 2152 rows of ETH/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:18:50,210 - [BITVAVO] - root - INFO - Last timestamp in cache for ETH/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:18:50,212 - [BITVAVO] - root - INFO - Expected last timestamp for ETH/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:18:50,212 - [BITVAVO] - root - INFO - Data is up to date for ETH/USDT
2025-07-11 07:18:50,215 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:18:50,247 - [BITVAVO] - root - INFO - Loaded 2152 rows of BTC/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:18:50,248 - [BITVAVO] - root - INFO - Last timestamp in cache for BTC/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:18:50,248 - [BITVAVO] - root - INFO - Expected last timestamp for BTC/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:18:50,248 - [BITVAVO] - root - INFO - Data is up to date for BTC/USDT
2025-07-11 07:18:50,250 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:18:50,297 - [BITVAVO] - root - INFO - Loaded 1795 rows of SOL/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:18:50,300 - [BITVAVO] - root - INFO - Last timestamp in cache for SOL/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:18:50,300 - [BITVAVO] - root - INFO - Expected last timestamp for SOL/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:18:50,302 - [BITVAVO] - root - INFO - Data is up to date for SOL/USDT
2025-07-11 07:18:50,304 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:18:50,324 - [BITVAVO] - root - INFO - Loaded 800 rows of SUI/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:18:50,325 - [BITVAVO] - root - INFO - Last timestamp in cache for SUI/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:18:50,326 - [BITVAVO] - root - INFO - Expected last timestamp for SUI/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:18:50,326 - [BITVAVO] - root - INFO - Data is up to date for SUI/USDT
2025-07-11 07:18:50,327 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:18:50,356 - [BITVAVO] - root - INFO - Loaded 2152 rows of XRP/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:18:50,356 - [BITVAVO] - root - INFO - Last timestamp in cache for XRP/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:18:50,357 - [BITVAVO] - root - INFO - Expected last timestamp for XRP/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:18:50,357 - [BITVAVO] - root - INFO - Data is up to date for XRP/USDT
2025-07-11 07:18:50,359 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:18:50,382 - [BITVAVO] - root - INFO - Loaded 1730 rows of AAVE/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:18:50,383 - [BITVAVO] - root - INFO - Last timestamp in cache for AAVE/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:18:50,384 - [BITVAVO] - root - INFO - Expected last timestamp for AAVE/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:18:50,384 - [BITVAVO] - root - INFO - Data is up to date for AAVE/USDT
2025-07-11 07:18:50,386 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:18:50,412 - [BITVAVO] - root - INFO - Loaded 1753 rows of AVAX/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:18:50,413 - [BITVAVO] - root - INFO - Last timestamp in cache for AVAX/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:18:50,413 - [BITVAVO] - root - INFO - Expected last timestamp for AVAX/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:18:50,414 - [BITVAVO] - root - INFO - Data is up to date for AVAX/USDT
2025-07-11 07:18:50,415 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:18:50,436 - [BITVAVO] - root - INFO - Loaded 2152 rows of ADA/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:18:50,437 - [BITVAVO] - root - INFO - Last timestamp in cache for ADA/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:18:50,437 - [BITVAVO] - root - INFO - Expected last timestamp for ADA/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:18:50,438 - [BITVAVO] - root - INFO - Data is up to date for ADA/USDT
2025-07-11 07:18:50,439 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:18:50,460 - [BITVAVO] - root - INFO - Loaded 2152 rows of LINK/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:18:50,461 - [BITVAVO] - root - INFO - Last timestamp in cache for LINK/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:18:50,462 - [BITVAVO] - root - INFO - Expected last timestamp for LINK/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:18:50,462 - [BITVAVO] - root - INFO - Data is up to date for LINK/USDT
2025-07-11 07:18:50,464 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:18:50,487 - [BITVAVO] - root - INFO - Loaded 2152 rows of TRX/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:18:50,487 - [BITVAVO] - root - INFO - Last timestamp in cache for TRX/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:18:50,488 - [BITVAVO] - root - INFO - Expected last timestamp for TRX/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:18:50,488 - [BITVAVO] - root - INFO - Data is up to date for TRX/USDT
2025-07-11 07:18:50,490 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:18:50,499 - [BITVAVO] - root - INFO - Loaded 798 rows of PEPE/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:18:50,500 - [BITVAVO] - root - INFO - Last timestamp in cache for PEPE/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:18:50,500 - [BITVAVO] - root - INFO - Expected last timestamp for PEPE/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:18:50,500 - [BITVAVO] - root - INFO - Data is up to date for PEPE/USDT
2025-07-11 07:18:50,501 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:18:50,519 - [BITVAVO] - root - INFO - Loaded 2152 rows of DOGE/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:18:50,520 - [BITVAVO] - root - INFO - Last timestamp in cache for DOGE/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:18:50,520 - [BITVAVO] - root - INFO - Expected last timestamp for DOGE/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:18:50,520 - [BITVAVO] - root - INFO - Data is up to date for DOGE/USDT
2025-07-11 07:18:50,521 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:18:50,540 - [BITVAVO] - root - INFO - Loaded 2152 rows of BNB/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:18:50,541 - [BITVAVO] - root - INFO - Last timestamp in cache for BNB/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:18:50,541 - [BITVAVO] - root - INFO - Expected last timestamp for BNB/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:18:50,542 - [BITVAVO] - root - INFO - Data is up to date for BNB/USDT
2025-07-11 07:18:50,543 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:18:50,560 - [BITVAVO] - root - INFO - Loaded 1788 rows of DOT/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:18:50,560 - [BITVAVO] - root - INFO - Last timestamp in cache for DOT/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:18:50,560 - [BITVAVO] - root - INFO - Expected last timestamp for DOT/USDT: 2025-07-10 00:00:00+00:00
2025-07-11 07:18:50,561 - [BITVAVO] - root - INFO - Data is up to date for DOT/USDT
2025-07-11 07:18:50,561 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:18:50,562 - [BITVAVO] - root - INFO - Using 14 trend assets (USDT) for analysis and 14 trading assets (EUR) for execution
2025-07-11 07:18:50,562 - [BITVAVO] - root - INFO - MTPI Multi-Indicator Configuration:
2025-07-11 07:18:50,563 - [BITVAVO] - root - INFO -   - Number of indicators: 8
2025-07-11 07:18:50,563 - [BITVAVO] - root - INFO -   - Enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-11 07:18:50,563 - [BITVAVO] - root - INFO -   - Combination method: consensus
2025-07-11 07:18:50,563 - [BITVAVO] - root - INFO -   - Long threshold: 0.1
2025-07-11 07:18:50,563 - [BITVAVO] - root - INFO -   - Short threshold: -0.1
2025-07-11 07:18:50,563 - [BITVAVO] - root - ERROR - [DEBUG] BACKGROUND SERVICE - trend_assets order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-11 07:18:50,563 - [BITVAVO] - root - ERROR - [DEBUG] BACKGROUND SERVICE - trading_assets order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 07:18:50,563 - [BITVAVO] - root - ERROR - [DEBUG] BACKGROUND SERVICE - BTC position in trend_assets: 2
2025-07-11 07:18:50,563 - [BITVAVO] - root - ERROR - [DEBUG] BACKGROUND SERVICE - TRX position in trend_assets: 10
2025-07-11 07:18:50,563 - [BITVAVO] - root - INFO - === RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===
2025-07-11 07:18:50,563 - [BITVAVO] - root - INFO - Parameters: use_mtpi_signal=True, mtpi_indicator_type=PGO
2025-07-11 07:18:50,563 - [BITVAVO] - root - INFO - mtpi_timeframe=1d, mtpi_pgo_length=35
2025-07-11 07:18:50,563 - [BITVAVO] - root - INFO - mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58
2025-07-11 07:18:50,563 - [BITVAVO] - root - INFO - timeframe=1d, analysis_start_date='2025-02-10'
2025-07-11 07:18:50,563 - [BITVAVO] - root - INFO - n_assets=1, use_weighted_allocation=False
2025-07-11 07:18:50,563 - [BITVAVO] - root - INFO - Using provided trend method: PGO For Loop
2025-07-11 07:18:50,563 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 07:18:50,576 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:18:50,577 - [BITVAVO] - root - INFO - Saving configuration to config/settings_bitvavo_eur.yaml...
2025-07-11 07:18:50,597 - [BITVAVO] - root - INFO - Configuration saved successfully.
2025-07-11 07:18:50,598 - [BITVAVO] - root - INFO - Trend detection assets (USDT): ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-11 07:18:50,598 - [BITVAVO] - root - INFO - Number of trend detection assets: 14
2025-07-11 07:18:50,599 - [BITVAVO] - root - INFO - Selected assets type: <class 'list'>
2025-07-11 07:18:50,599 - [BITVAVO] - root - INFO - Trading execution assets (EUR): ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 07:18:50,599 - [BITVAVO] - root - INFO - Number of trading assets: 14
2025-07-11 07:18:50,600 - [BITVAVO] - root - INFO - Trading assets type: <class 'list'>
2025-07-11 07:18:50,964 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-11 07:18:50,979 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:18:50,993 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-11 07:18:51,008 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:18:51,008 - [BITVAVO] - root - INFO - Execution context: backtesting
2025-07-11 07:18:51,008 - [BITVAVO] - root - INFO - Execution timing: candle_close
2025-07-11 07:18:51,008 - [BITVAVO] - root - INFO - Ratio calculation method: independent
2025-07-11 07:18:51,009 - [BITVAVO] - root - INFO - Tie-breaking strategy: imcumbent
2025-07-11 07:18:51,009 - [BITVAVO] - root - INFO - Asset trend PGO parameters: length=None, upper_threshold=None, lower_threshold=None
2025-07-11 07:18:51,009 - [BITVAVO] - root - INFO - MTPI indicators override: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-11 07:18:51,009 - [BITVAVO] - root - INFO - MTPI combination method override: consensus
2025-07-11 07:18:51,009 - [BITVAVO] - root - INFO - MTPI long threshold override: 0.1
2025-07-11 07:18:51,009 - [BITVAVO] - root - INFO - MTPI short threshold override: -0.1
2025-07-11 07:18:51,009 - [BITVAVO] - root - INFO - Using standard warmup period of 60 days for equal allocation
2025-07-11 07:18:51,009 - [BITVAVO] - root - INFO - Fetching data from 2024-12-12 to ensure proper warmup (analysis starts at 2025-02-10)
2025-07-11 07:18:51,012 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:18:51,012 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:18:51,012 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:18:51,012 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:18:51,013 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:18:51,013 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:18:51,013 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:18:51,013 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:18:51,014 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:18:51,014 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:18:51,014 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:18:51,014 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:18:51,014 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:18:51,015 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-11 07:18:51,015 - [BITVAVO] - root - INFO - Checking cache for 14 symbols (1d)...
2025-07-11 07:18:51,043 - [BITVAVO] - root - INFO - Loaded 211 rows of ETH/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:18:51,044 - [BITVAVO] - root - INFO - Metadata for ETH/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:18:51,045 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:18:51,045 - [BITVAVO] - root - INFO - Loaded 211 rows of ETH/USDT data from cache (after filtering).
2025-07-11 07:18:51,071 - [BITVAVO] - root - INFO - Loaded 211 rows of BTC/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:18:51,072 - [BITVAVO] - root - INFO - Metadata for BTC/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:18:51,073 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:18:51,073 - [BITVAVO] - root - INFO - Loaded 211 rows of BTC/USDT data from cache (after filtering).
2025-07-11 07:18:51,091 - [BITVAVO] - root - INFO - Loaded 211 rows of SOL/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:18:51,092 - [BITVAVO] - root - INFO - Metadata for SOL/USDT shows data starting from 2020-08-11, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:18:51,092 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:18:51,092 - [BITVAVO] - root - INFO - Loaded 211 rows of SOL/USDT data from cache (after filtering).
2025-07-11 07:18:51,103 - [BITVAVO] - root - INFO - Loaded 211 rows of SUI/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:18:51,104 - [BITVAVO] - root - INFO - Metadata for SUI/USDT shows data starting from 2023-05-03, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:18:51,105 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:18:51,105 - [BITVAVO] - root - INFO - Loaded 211 rows of SUI/USDT data from cache (after filtering).
2025-07-11 07:18:51,124 - [BITVAVO] - root - INFO - Loaded 211 rows of XRP/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:18:51,125 - [BITVAVO] - root - INFO - Metadata for XRP/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:18:51,125 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:18:51,125 - [BITVAVO] - root - INFO - Loaded 211 rows of XRP/USDT data from cache (after filtering).
2025-07-11 07:18:51,142 - [BITVAVO] - root - INFO - Loaded 211 rows of AAVE/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:18:51,143 - [BITVAVO] - root - INFO - Metadata for AAVE/USDT shows data starting from 2020-10-15, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:18:51,143 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:18:51,143 - [BITVAVO] - root - INFO - Loaded 211 rows of AAVE/USDT data from cache (after filtering).
2025-07-11 07:18:51,161 - [BITVAVO] - root - INFO - Loaded 211 rows of AVAX/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:18:51,162 - [BITVAVO] - root - INFO - Metadata for AVAX/USDT shows data starting from 2020-09-22, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:18:51,162 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:18:51,162 - [BITVAVO] - root - INFO - Loaded 211 rows of AVAX/USDT data from cache (after filtering).
2025-07-11 07:18:51,180 - [BITVAVO] - root - INFO - Loaded 211 rows of ADA/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:18:51,181 - [BITVAVO] - root - INFO - Metadata for ADA/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:18:51,182 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:18:51,182 - [BITVAVO] - root - INFO - Loaded 211 rows of ADA/USDT data from cache (after filtering).
2025-07-11 07:18:51,202 - [BITVAVO] - root - INFO - Loaded 211 rows of LINK/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:18:51,203 - [BITVAVO] - root - INFO - Metadata for LINK/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:18:51,203 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:18:51,203 - [BITVAVO] - root - INFO - Loaded 211 rows of LINK/USDT data from cache (after filtering).
2025-07-11 07:18:51,225 - [BITVAVO] - root - INFO - Loaded 211 rows of TRX/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:18:51,226 - [BITVAVO] - root - INFO - Metadata for TRX/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:18:51,226 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:18:51,226 - [BITVAVO] - root - INFO - Loaded 211 rows of TRX/USDT data from cache (after filtering).
2025-07-11 07:18:51,237 - [BITVAVO] - root - INFO - Loaded 211 rows of PEPE/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:18:51,238 - [BITVAVO] - root - INFO - Metadata for PEPE/USDT shows data starting from 2023-05-05, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:18:51,239 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:18:51,239 - [BITVAVO] - root - INFO - Loaded 211 rows of PEPE/USDT data from cache (after filtering).
2025-07-11 07:18:51,270 - [BITVAVO] - root - INFO - Loaded 211 rows of DOGE/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:18:51,271 - [BITVAVO] - root - INFO - Metadata for DOGE/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:18:51,271 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:18:51,272 - [BITVAVO] - root - INFO - Loaded 211 rows of DOGE/USDT data from cache (after filtering).
2025-07-11 07:18:51,290 - [BITVAVO] - root - INFO - Loaded 211 rows of BNB/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:18:51,291 - [BITVAVO] - root - INFO - Metadata for BNB/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:18:51,292 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:18:51,292 - [BITVAVO] - root - INFO - Loaded 211 rows of BNB/USDT data from cache (after filtering).
2025-07-11 07:18:51,310 - [BITVAVO] - root - INFO - Loaded 211 rows of DOT/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:18:51,311 - [BITVAVO] - root - INFO - Metadata for DOT/USDT shows data starting from 2020-08-18, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-11 07:18:51,312 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:18:51,312 - [BITVAVO] - root - INFO - Loaded 211 rows of DOT/USDT data from cache (after filtering).
2025-07-11 07:18:51,312 - [BITVAVO] - root - INFO - All 14 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-11 07:18:51,313 - [BITVAVO] - root - INFO - Asset ETH/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:18:51,313 - [BITVAVO] - root - INFO - Asset BTC/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:18:51,313 - [BITVAVO] - root - INFO - Asset SOL/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:18:51,313 - [BITVAVO] - root - INFO - Asset SUI/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:18:51,313 - [BITVAVO] - root - INFO - Asset XRP/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:18:51,313 - [BITVAVO] - root - INFO - Asset AAVE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:18:51,313 - [BITVAVO] - root - INFO - Asset AVAX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:18:51,314 - [BITVAVO] - root - INFO - Asset ADA/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:18:51,314 - [BITVAVO] - root - INFO - Asset LINK/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:18:51,314 - [BITVAVO] - root - INFO - Asset TRX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:18:51,314 - [BITVAVO] - root - INFO - Asset PEPE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:18:51,314 - [BITVAVO] - root - INFO - Asset DOGE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:18:51,314 - [BITVAVO] - root - INFO - Asset BNB/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:18:51,315 - [BITVAVO] - root - INFO - Asset DOT/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-11 07:18:51,358 - [BITVAVO] - root - INFO - Using standard MTPI warmup period of 120 days
2025-07-11 07:18:51,358 - [BITVAVO] - root - INFO - Fetching MTPI signals from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-07-11 07:18:51,359 - [BITVAVO] - root - INFO - Fetching MTPI signals using trend method: PGO For Loop
2025-07-11 07:18:51,359 - [BITVAVO] - root - INFO - Using multi-indicator MTPI with config override: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-07-11 07:18:51,359 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-11 07:18:51,372 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:18:51,372 - [BITVAVO] - root - INFO - Loaded MTPI multi-indicator configuration with 8 enabled indicators
2025-07-11 07:18:51,373 - [BITVAVO] - root - INFO - Applying configuration overrides: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-07-11 07:18:51,373 - [BITVAVO] - root - INFO - Override: enabled_indicators = ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-11 07:18:51,373 - [BITVAVO] - root - INFO - Override: combination_method = consensus
2025-07-11 07:18:51,373 - [BITVAVO] - root - INFO - Override: long_threshold = 0.1
2025-07-11 07:18:51,373 - [BITVAVO] - root - INFO - Override: short_threshold = -0.1
2025-07-11 07:18:51,373 - [BITVAVO] - root - INFO - Using MTPI configuration: indicators=['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], method=consensus, thresholds=(-0.1, 0.1)
2025-07-11 07:18:51,373 - [BITVAVO] - root - INFO - Calculated appropriate limit for 1d timeframe: 500 candles (minimum 180.0 candles needed for 60 length indicator)
2025-07-11 07:18:51,373 - [BITVAVO] - root - INFO - Using adjusted limit: 500 for max indicator length: 60
2025-07-11 07:18:51,373 - [BITVAVO] - root - INFO - Checking cache for 1 symbols (1d)...
2025-07-11 07:18:51,394 - [BITVAVO] - root - INFO - Loaded 271 rows of BTC/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:18:51,394 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:18:51,395 - [BITVAVO] - root - INFO - Loaded 271 rows of BTC/USDT data from cache (after filtering).
2025-07-11 07:18:51,395 - [BITVAVO] - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-11 07:18:51,395 - [BITVAVO] - root - INFO - Fetched BTC data: 271 candles from 2024-10-13 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:18:51,395 - [BITVAVO] - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-07-11 07:18:51,468 - [BITVAVO] - root - INFO - Generated PGO Score signals: {-1: np.int64(111), 0: np.int64(34), 1: np.int64(126)}
2025-07-11 07:18:51,469 - [BITVAVO] - root - INFO - Generated pgo signals: 271 values
2025-07-11 07:18:51,469 - [BITVAVO] - root - INFO - Generating Bollinger Band signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False, heikin_src=close
2025-07-11 07:18:51,469 - [BITVAVO] - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-07-11 07:18:51,492 - [BITVAVO] - root - INFO - Generated BB Score signals: {-1: np.int64(109), 0: np.int64(32), 1: np.int64(130)}
2025-07-11 07:18:51,492 - [BITVAVO] - root - INFO - Generated Bollinger Band signals: 271 values
2025-07-11 07:18:51,492 - [BITVAVO] - root - INFO - Generated bollinger_bands signals: 271 values
2025-07-11 07:18:52,086 - [BITVAVO] - root - INFO - Generated DWMA signals using Weighted SD method
2025-07-11 07:18:52,086 - [BITVAVO] - root - INFO - Generated dwma_score signals: 271 values
2025-07-11 07:18:52,168 - [BITVAVO] - root - INFO - Generated DEMA Supertrend signals
2025-07-11 07:18:52,168 - [BITVAVO] - root - INFO - Signal distribution: {-1: np.int64(156), 0: np.int64(1), 1: np.int64(114)}
2025-07-11 07:18:52,168 - [BITVAVO] - root - INFO - Generated DEMA Super Score signals
2025-07-11 07:18:52,169 - [BITVAVO] - root - INFO - Generated dema_super_score signals: 271 values
2025-07-11 07:18:52,330 - [BITVAVO] - root - INFO - Generated DPSD signals
2025-07-11 07:18:52,330 - [BITVAVO] - root - INFO - Signal distribution: {-1: np.int64(105), 0: np.int64(87), 1: np.int64(79)}
2025-07-11 07:18:52,330 - [BITVAVO] - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-07-11 07:18:52,330 - [BITVAVO] - root - INFO - Generated dpsd_score signals: 271 values
2025-07-11 07:18:52,343 - [BITVAVO] - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-07-11 07:18:52,343 - [BITVAVO] - root - INFO - Generated AAD Score signals using SMA method
2025-07-11 07:18:52,344 - [BITVAVO] - root - INFO - Generated aad_score signals: 271 values
2025-07-11 07:18:52,433 - [BITVAVO] - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-07-11 07:18:52,433 - [BITVAVO] - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-07-11 07:18:52,433 - [BITVAVO] - root - INFO - Generated dynamic_ema_score signals: 271 values
2025-07-11 07:18:52,592 - [BITVAVO] - root - INFO - Generated quantile_dema_score signals: 271 values
2025-07-11 07:18:52,603 - [BITVAVO] - root - INFO - Combined 8 signals using arithmetic mean aggregation
2025-07-11 07:18:52,603 - [BITVAVO] - root - INFO - Signal distribution: {1: 146, -1: 124, 0: 1}
2025-07-11 07:18:52,604 - [BITVAVO] - root - INFO - Generated combined MTPI signals: 271 values using consensus method
2025-07-11 07:18:52,604 - [BITVAVO] - root - INFO - Signal distribution: {1: 146, -1: 124, 0: 1}
2025-07-11 07:18:52,604 - [BITVAVO] - root - INFO - Calculating daily scores using trend method: PGO For Loop...
2025-07-11 07:18:52,607 - [BITVAVO] - root - INFO - Saving configuration to config/settings.yaml...
2025-07-11 07:18:52,614 - [BITVAVO] - root - INFO - Configuration saved successfully.
2025-07-11 07:18:52,615 - [BITVAVO] - root - INFO - Updated config with trend method: PGO For Loop and PGO parameters
2025-07-11 07:18:52,615 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-11 07:18:52,626 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:18:52,626 - [BITVAVO] - root - INFO - Using ratio-based approach with PGO (PGO For Loop)...
2025-07-11 07:18:52,626 - [BITVAVO] - root - INFO - Calculating ratio PGO signals for 14 assets with PGO(35) using full OHLCV data
2025-07-11 07:18:52,626 - [BITVAVO] - root - INFO - Using ratio calculation method: independent
2025-07-11 07:18:52,659 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:52,696 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:18:52,727 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:18:52,727 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:52,762 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:18:52,770 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:18:52,802 - [BITVAVO] - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-11 07:18:52,802 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:52,828 - [BITVAVO] - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-11 07:18:52,836 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:18:52,867 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:18:52,867 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:52,896 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:18:52,906 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:18:52,942 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 07:18:52,942 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:52,976 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 07:18:52,987 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:18:53,028 - [BITVAVO] - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-07-11 07:18:53,028 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:53,057 - [BITVAVO] - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-07-11 07:18:53,066 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:18:53,098 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 07:18:53,098 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:53,129 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 07:18:53,143 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:18:53,188 - [BITVAVO] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-11 07:18:53,188 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:53,229 - [BITVAVO] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-11 07:18:53,240 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:18:53,271 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-11 07:18:53,272 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:53,302 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-11 07:18:53,311 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:18:53,346 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 07:18:53,347 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:53,376 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 07:18:53,386 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:18:53,437 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:18:53,441 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:53,492 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:18:53,502 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:18:53,534 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:53,581 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:18:53,618 - [BITVAVO] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-11 07:18:53,618 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:53,647 - [BITVAVO] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-11 07:18:53,656 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:18:53,688 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 07:18:53,688 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:53,716 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 07:18:53,726 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:18:53,762 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:53,806 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:18:53,844 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 07:18:53,845 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:53,893 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 07:18:53,905 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:18:53,944 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 07:18:53,945 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:53,977 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 07:18:53,988 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:18:54,025 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 07:18:54,025 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:54,055 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 07:18:54,064 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:18:54,102 - [BITVAVO] - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-07-11 07:18:54,102 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:54,135 - [BITVAVO] - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-07-11 07:18:54,145 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:18:54,179 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:18:54,179 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:54,206 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:18:54,215 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:18:54,245 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 07:18:54,245 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:54,274 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 07:18:54,284 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:18:54,318 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:18:54,318 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:54,345 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:18:54,355 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:18:54,387 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 07:18:54,387 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:54,416 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 07:18:54,427 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:18:54,461 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:54,498 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:18:54,530 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:54,568 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:18:54,601 - [BITVAVO] - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-11 07:18:54,602 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:54,630 - [BITVAVO] - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-11 07:18:54,639 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:18:54,673 - [BITVAVO] - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-07-11 07:18:54,674 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:54,701 - [BITVAVO] - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-07-11 07:18:54,710 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:18:54,741 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:54,775 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:18:54,804 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:18:54,805 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:54,832 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:18:54,841 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:18:54,874 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:18:54,875 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:54,902 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:18:54,912 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:18:54,943 - [BITVAVO] - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-07-11 07:18:54,943 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:54,971 - [BITVAVO] - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-07-11 07:18:54,980 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:18:55,011 - [BITVAVO] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-11 07:18:55,011 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:55,037 - [BITVAVO] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-11 07:18:55,047 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:18:55,077 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 07:18:55,077 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:55,104 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 07:18:55,113 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:18:55,143 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 07:18:55,143 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:55,171 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 07:18:55,181 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:18:55,214 - [BITVAVO] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-11 07:18:55,214 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:55,242 - [BITVAVO] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-11 07:18:55,251 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:18:55,285 - [BITVAVO] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-11 07:18:55,285 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:55,314 - [BITVAVO] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-11 07:18:55,333 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:18:55,370 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:18:55,370 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:55,402 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:18:55,412 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:18:55,445 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:18:55,446 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:55,477 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:18:55,486 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:18:55,517 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:18:55,517 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:55,546 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:18:55,556 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:18:55,585 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:18:55,586 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:55,612 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:18:55,622 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:18:55,655 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:55,697 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:18:55,726 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:55,762 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:18:55,796 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:55,855 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:18:55,907 - [BITVAVO] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-11 07:18:55,907 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:55,954 - [BITVAVO] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-11 07:18:55,969 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:18:56,016 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:56,052 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:18:56,083 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:56,123 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:18:56,154 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:18:56,154 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:56,183 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:18:56,192 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:18:56,227 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:56,264 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:18:56,297 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:56,335 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:18:56,368 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:18:56,368 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:56,395 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:18:56,404 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:18:56,437 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:56,475 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:18:56,506 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:56,545 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:18:56,590 - [BITVAVO] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-11 07:18:56,590 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:56,620 - [BITVAVO] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-11 07:18:56,630 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:18:56,661 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:56,715 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:18:56,755 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:56,797 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:18:56,831 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:18:56,831 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:56,863 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:18:56,873 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:18:56,908 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:18:56,908 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:56,947 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:18:56,959 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:18:56,994 - [BITVAVO] - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-07-11 07:18:56,995 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:57,028 - [BITVAVO] - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-07-11 07:18:57,038 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:18:57,071 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-11 07:18:57,071 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:57,100 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-11 07:18:57,109 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:18:57,141 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:18:57,141 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:57,170 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:18:57,180 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:18:57,220 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:18:57,220 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:57,247 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:18:57,255 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:18:57,284 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:18:57,285 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:57,311 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-11 07:18:57,321 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:18:57,354 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:57,390 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:18:57,424 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:57,487 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:18:57,518 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-11 07:18:57,518 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:57,552 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-11 07:18:57,561 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:18:57,592 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:57,634 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:18:57,670 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:57,706 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:18:57,739 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:57,779 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:18:57,816 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:57,859 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:18:57,891 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:57,933 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:18:57,970 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:18:57,971 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:57,999 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:18:58,008 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:18:58,042 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:18:58,042 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:58,078 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:18:58,088 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:18:58,119 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 07:18:58,119 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:58,149 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 07:18:58,159 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:18:58,191 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:58,234 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:18:58,268 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:58,317 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:18:58,354 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-11 07:18:58,354 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:58,385 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-11 07:18:58,396 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:18:58,429 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:58,465 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:18:58,500 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:18:58,500 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:58,528 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:18:58,537 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:18:58,570 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:58,613 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:18:58,645 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:58,683 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:18:58,714 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:58,752 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:18:58,784 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:58,822 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:18:58,854 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:58,894 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:18:58,925 - [BITVAVO] - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-11 07:18:58,926 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:58,957 - [BITVAVO] - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-11 07:18:58,967 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:18:58,998 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:59,035 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:18:59,068 - [BITVAVO] - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-07-11 07:18:59,068 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:59,098 - [BITVAVO] - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-07-11 07:18:59,107 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:18:59,141 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:59,180 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:18:59,215 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:59,256 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:18:59,293 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:59,333 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:18:59,372 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:59,421 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:18:59,456 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:59,496 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:18:59,528 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:18:59,528 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:59,558 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:18:59,569 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:18:59,602 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:59,638 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:18:59,691 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:59,730 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:18:59,765 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-11 07:18:59,766 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:59,803 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-11 07:18:59,812 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:18:59,846 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:59,889 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:18:59,939 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-11 07:18:59,940 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:18:59,990 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-11 07:19:00,007 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:19:00,059 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 07:19:00,060 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:00,103 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 07:19:00,113 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:19:00,143 - [BITVAVO] - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-11 07:19:00,144 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:00,172 - [BITVAVO] - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-11 07:19:00,181 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:19:00,211 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:00,247 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:19:00,280 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:19:00,280 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:00,308 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:19:00,316 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:19:00,349 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:19:00,350 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:00,376 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:19:00,386 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:19:00,415 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:00,453 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:19:00,485 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:19:00,485 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:00,514 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:19:00,523 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:19:00,559 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:00,605 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:19:00,641 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:00,686 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:19:00,716 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:00,752 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:19:00,783 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:00,818 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:19:00,846 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:00,883 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:19:00,914 - [BITVAVO] - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-07-11 07:19:00,914 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:00,943 - [BITVAVO] - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-07-11 07:19:00,953 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:19:00,988 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:01,028 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:19:01,061 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:01,098 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:19:01,131 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:01,167 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:19:01,214 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:19:01,215 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:01,243 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:19:01,253 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:19:01,287 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:01,329 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:19:01,362 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:01,400 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:19:01,434 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:01,472 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:19:01,507 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:01,546 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:19:01,595 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:01,649 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:19:01,685 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:19:01,685 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:01,711 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:19:01,720 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:19:01,753 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:01,793 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:19:01,828 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:19:01,829 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:01,857 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:19:01,866 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:19:01,901 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:19:01,902 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:01,930 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:19:01,942 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:19:01,986 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 07:19:01,987 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:02,043 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 07:19:02,054 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:19:02,087 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:02,127 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:19:02,165 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:19:02,165 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:02,199 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:19:02,209 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:19:02,241 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:19:02,241 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:02,269 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:19:02,281 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:19:02,316 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:02,356 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:19:02,395 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:02,438 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:19:02,497 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:02,541 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:19:02,576 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:02,616 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:19:02,650 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:02,687 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:19:02,718 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:02,755 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:19:02,791 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:02,832 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:19:02,865 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:02,907 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:19:02,946 - [BITVAVO] - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-07-11 07:19:02,946 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:02,989 - [BITVAVO] - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-07-11 07:19:03,001 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:19:03,034 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 07:19:03,035 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:03,066 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-11 07:19:03,077 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:19:03,121 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:19:03,121 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:03,174 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-11 07:19:03,190 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:19:03,245 - [BITVAVO] - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-07-11 07:19:03,245 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:03,292 - [BITVAVO] - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-07-11 07:19:03,306 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:19:03,360 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:03,419 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:19:03,461 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:03,500 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:19:03,534 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:03,573 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:19:03,605 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:03,644 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:19:03,679 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:03,716 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:19:03,760 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:03,803 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:19:03,837 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:03,874 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:19:03,906 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 07:19:03,907 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:03,941 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 07:19:03,951 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:19:03,981 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:19:03,981 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:04,009 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-11 07:19:04,018 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:19:04,048 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:19:04,048 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:04,081 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:19:04,092 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:19:04,126 - [BITVAVO] - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-07-11 07:19:04,127 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:04,154 - [BITVAVO] - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-07-11 07:19:04,163 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:19:04,194 - [BITVAVO] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-11 07:19:04,195 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:04,226 - [BITVAVO] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-11 07:19:04,236 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:19:04,267 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:19:04,268 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:04,295 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:19:04,304 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:19:04,334 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-11 07:19:04,334 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:04,361 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-11 07:19:04,370 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:19:04,401 - [BITVAVO] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-11 07:19:04,401 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:04,431 - [BITVAVO] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-11 07:19:04,440 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:19:04,474 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:04,511 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:19:04,541 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:19:04,541 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:04,569 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:19:04,578 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:19:04,609 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:04,646 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:19:04,679 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:04,714 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:19:04,745 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:19:04,745 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:04,775 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:19:04,783 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:19:04,812 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:19:04,813 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:04,838 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:19:04,847 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:19:04,876 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 07:19:04,876 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:04,903 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-11 07:19:04,912 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:19:04,942 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-11 07:19:04,942 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:04,969 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-11 07:19:04,978 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:19:05,007 - [BITVAVO] - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-07-11 07:19:05,007 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:05,037 - [BITVAVO] - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-07-11 07:19:05,046 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:19:05,088 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:19:05,088 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:05,120 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:19:05,130 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:19:05,165 - [BITVAVO] - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-07-11 07:19:05,165 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:05,196 - [BITVAVO] - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-07-11 07:19:05,205 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:19:05,241 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:19:05,242 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:05,270 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-11 07:19:05,281 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:19:05,315 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:19:05,316 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:05,345 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:19:05,354 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:19:05,386 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:05,426 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:19:05,457 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:05,499 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOT/USDT using full OHLCV data
2025-07-11 07:19:05,535 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:05,579 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ETH/USDT using full OHLCV data
2025-07-11 07:19:05,621 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:05,662 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BTC/USDT using full OHLCV data
2025-07-11 07:19:05,696 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:05,737 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SOL/USDT using full OHLCV data
2025-07-11 07:19:05,773 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:19:05,774 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:05,801 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-11 07:19:05,810 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SUI/USDT using full OHLCV data
2025-07-11 07:19:05,843 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 07:19:05,843 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:05,877 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-11 07:19:05,895 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/XRP/USDT using full OHLCV data
2025-07-11 07:19:05,927 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 07:19:05,927 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:05,954 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-11 07:19:05,963 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AAVE/USDT using full OHLCV data
2025-07-11 07:19:05,992 - [BITVAVO] - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-07-11 07:19:05,993 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:06,021 - [BITVAVO] - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-07-11 07:19:06,029 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AVAX/USDT using full OHLCV data
2025-07-11 07:19:06,074 - [BITVAVO] - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-07-11 07:19:06,075 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:06,109 - [BITVAVO] - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-07-11 07:19:06,123 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ADA/USDT using full OHLCV data
2025-07-11 07:19:06,154 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-11 07:19:06,154 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:06,183 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-11 07:19:06,192 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/LINK/USDT using full OHLCV data
2025-07-11 07:19:06,226 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:06,266 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/TRX/USDT using full OHLCV data
2025-07-11 07:19:06,297 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:19:06,297 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:06,327 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-11 07:19:06,336 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/PEPE/USDT using full OHLCV data
2025-07-11 07:19:06,369 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:06,409 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/DOGE/USDT using full OHLCV data
2025-07-11 07:19:06,445 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-11 07:19:06,487 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BNB/USDT using full OHLCV data
2025-07-11 07:19:10,205 - [BITVAVO] - root - INFO - Successfully calculated daily scores using ratio-based comparisons.
2025-07-11 07:19:10,206 - [BITVAVO] - root - INFO - Latest MTPI signal is 1
2025-07-11 07:19:10,206 - [BITVAVO] - root - INFO - Latest MTPI signal is bullish (1), will proceed with normal asset selection
2025-07-11 07:19:10,206 - [BITVAVO] - root - INFO - Finished calculating daily scores. DataFrame shape: (211, 14)
2025-07-11 07:19:10,206 - [BITVAVO] - root - WARNING - Running strategy with n_assets=1, equal allocation, MTPI filtering ENABLED
2025-07-11 07:19:10,213 - [BITVAVO] - root - INFO - Date ranges for each asset:
2025-07-11 07:19:10,213 - [BITVAVO] - root - INFO -   ETH/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:19:10,213 - [BITVAVO] - root - INFO -   BTC/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:19:10,213 - [BITVAVO] - root - INFO -   SOL/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:19:10,213 - [BITVAVO] - root - INFO -   SUI/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:19:10,214 - [BITVAVO] - root - INFO -   XRP/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:19:10,214 - [BITVAVO] - root - INFO -   AAVE/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:19:10,214 - [BITVAVO] - root - INFO -   AVAX/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:19:10,214 - [BITVAVO] - root - INFO -   ADA/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:19:10,214 - [BITVAVO] - root - INFO -   LINK/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:19:10,214 - [BITVAVO] - root - INFO -   TRX/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:19:10,214 - [BITVAVO] - root - INFO -   PEPE/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:19:10,214 - [BITVAVO] - root - INFO -   DOGE/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:19:10,214 - [BITVAVO] - root - INFO -   BNB/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:19:10,214 - [BITVAVO] - root - INFO -   DOT/USDT: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:19:10,214 - [BITVAVO] - root - INFO - Common dates range: 2024-12-12 to 2025-07-10 (211 candles)
2025-07-11 07:19:10,215 - [BITVAVO] - root - INFO - Analysis will run from: 2025-02-10 to 2025-07-10 (151 candles)
2025-07-11 07:19:10,220 - [BITVAVO] - root - INFO - EXECUTION TIMING VERIFICATION:
2025-07-11 07:19:10,220 - [BITVAVO] - root - INFO -    Execution Method: candle_close
2025-07-11 07:19:10,220 - [BITVAVO] - root - INFO -    Automatic execution at 00:00 UTC (candle close)
2025-07-11 07:19:10,221 - [BITVAVO] - root - INFO -    Signal generated and executed immediately
2025-07-11 07:19:10,232 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-10 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 12.0, 'SOL/USDT': 10.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 4.0, 'LINK/USDT': 8.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 6.0}
2025-07-11 07:19:10,232 - [BITVAVO] - root - INFO - Including ETH/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,233 - [BITVAVO] - root - INFO - Including BTC/USDT on 2025-02-10 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,233 - [BITVAVO] - root - INFO - Including SOL/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,233 - [BITVAVO] - root - INFO - Including SUI/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,233 - [BITVAVO] - root - INFO - Including XRP/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,233 - [BITVAVO] - root - INFO - Including AAVE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,233 - [BITVAVO] - root - INFO - Including AVAX/USDT on 2025-02-10 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,233 - [BITVAVO] - root - INFO - Including ADA/USDT on 2025-02-10 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,233 - [BITVAVO] - root - INFO - Including LINK/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,233 - [BITVAVO] - root - INFO - Including TRX/USDT on 2025-02-10 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,233 - [BITVAVO] - root - INFO - Including PEPE/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,233 - [BITVAVO] - root - INFO - Including DOGE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,233 - [BITVAVO] - root - INFO - Including BNB/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,233 - [BITVAVO] - root - INFO - Including DOT/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,234 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,234 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,234 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,234 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,235 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-11 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 12.0, 'SOL/USDT': 9.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 6.0, 'LINK/USDT': 8.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 4.0}
2025-07-11 07:19:10,235 - [BITVAVO] - root - INFO - Including ETH/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,235 - [BITVAVO] - root - INFO - Including BTC/USDT on 2025-02-11 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,235 - [BITVAVO] - root - INFO - Including SOL/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,235 - [BITVAVO] - root - INFO - Including SUI/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,236 - [BITVAVO] - root - INFO - Including XRP/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,236 - [BITVAVO] - root - INFO - Including AAVE/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,236 - [BITVAVO] - root - INFO - Including AVAX/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,236 - [BITVAVO] - root - INFO - Including ADA/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,236 - [BITVAVO] - root - INFO - Including LINK/USDT on 2025-02-11 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,236 - [BITVAVO] - root - INFO - Including TRX/USDT on 2025-02-11 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,236 - [BITVAVO] - root - INFO - Including PEPE/USDT on 2025-02-11 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,236 - [BITVAVO] - root - INFO - Including DOGE/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,236 - [BITVAVO] - root - INFO - Including BNB/USDT on 2025-02-11 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,236 - [BITVAVO] - root - INFO - Including DOT/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,236 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,236 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,237 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,237 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,238 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-12 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 11.0, 'SOL/USDT': 9.0, 'SUI/USDT': 4.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 6.0, 'LINK/USDT': 8.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 4.0}
2025-07-11 07:19:10,238 - [BITVAVO] - root - INFO - Including ETH/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,238 - [BITVAVO] - root - INFO - Including BTC/USDT on 2025-02-12 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,239 - [BITVAVO] - root - INFO - Including SOL/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,239 - [BITVAVO] - root - INFO - Including SUI/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,239 - [BITVAVO] - root - INFO - Including XRP/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,239 - [BITVAVO] - root - INFO - Including AAVE/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,239 - [BITVAVO] - root - INFO - Including AVAX/USDT on 2025-02-12 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,239 - [BITVAVO] - root - INFO - Including ADA/USDT on 2025-02-12 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,239 - [BITVAVO] - root - INFO - Including LINK/USDT on 2025-02-12 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,239 - [BITVAVO] - root - INFO - Including TRX/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,239 - [BITVAVO] - root - INFO - Including PEPE/USDT on 2025-02-12 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,239 - [BITVAVO] - root - INFO - Including DOGE/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,240 - [BITVAVO] - root - INFO - Including BNB/USDT on 2025-02-12 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,240 - [BITVAVO] - root - INFO - Including DOT/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,240 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,240 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,240 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,240 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,241 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-13 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 7.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 3.0}
2025-07-11 07:19:10,242 - [BITVAVO] - root - INFO - Including ETH/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,242 - [BITVAVO] - root - INFO - Including BTC/USDT on 2025-02-13 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,242 - [BITVAVO] - root - INFO - Including SOL/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,242 - [BITVAVO] - root - INFO - Including SUI/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,242 - [BITVAVO] - root - INFO - Including XRP/USDT on 2025-02-13 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,242 - [BITVAVO] - root - INFO - Including AAVE/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,242 - [BITVAVO] - root - INFO - Including AVAX/USDT on 2025-02-13 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,242 - [BITVAVO] - root - INFO - Including ADA/USDT on 2025-02-13 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,243 - [BITVAVO] - root - INFO - Including LINK/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,243 - [BITVAVO] - root - INFO - Including TRX/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,243 - [BITVAVO] - root - INFO - Including PEPE/USDT on 2025-02-13 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,243 - [BITVAVO] - root - INFO - Including DOGE/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,243 - [BITVAVO] - root - INFO - Including BNB/USDT on 2025-02-13 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,243 - [BITVAVO] - root - INFO - Including DOT/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,243 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,243 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,243 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,243 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,245 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-14 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 6.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 3.0}
2025-07-11 07:19:10,245 - [BITVAVO] - root - INFO - Including ETH/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,245 - [BITVAVO] - root - INFO - Including BTC/USDT on 2025-02-14 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,246 - [BITVAVO] - root - INFO - Including SOL/USDT on 2025-02-14 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,246 - [BITVAVO] - root - INFO - Including SUI/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,246 - [BITVAVO] - root - INFO - Including XRP/USDT on 2025-02-14 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,246 - [BITVAVO] - root - INFO - Including AAVE/USDT on 2025-02-14 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,246 - [BITVAVO] - root - INFO - Including AVAX/USDT on 2025-02-14 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,246 - [BITVAVO] - root - INFO - Including ADA/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,246 - [BITVAVO] - root - INFO - Including LINK/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,246 - [BITVAVO] - root - INFO - Including TRX/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,246 - [BITVAVO] - root - INFO - Including PEPE/USDT on 2025-02-14 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,246 - [BITVAVO] - root - INFO - Including DOGE/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,246 - [BITVAVO] - root - INFO - Including BNB/USDT on 2025-02-14 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,246 - [BITVAVO] - root - INFO - Including DOT/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-11 07:19:10,246 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,247 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,247 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,247 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,248 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-15 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 7.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 3.0}
2025-07-11 07:19:10,248 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,248 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,248 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,248 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,249 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-16 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 6.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 5.0}
2025-07-11 07:19:10,250 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,250 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,250 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,250 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,251 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-17 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 2.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 8.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 7.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 7.0}
2025-07-11 07:19:10,251 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,251 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,251 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,251 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,253 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-18 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 11.0, 'SOL/USDT': 2.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 8.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 7.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-11 07:19:10,253 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,253 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,253 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,253 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,254 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-19 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 11.0, 'SOL/USDT': 2.0, 'SUI/USDT': 5.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 8.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 8.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-11 07:19:10,254 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,255 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,255 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,255 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,256 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-20 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 1.0, 'SUI/USDT': 6.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 8.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 8.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 5.0}
2025-07-11 07:19:10,256 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,256 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,256 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,256 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,257 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-21 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 1.0, 'SUI/USDT': 6.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 7.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-11 07:19:10,257 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,258 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,258 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,258 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,259 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-22 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 6.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 13.0, 'DOT/USDT': 6.0}
2025-07-11 07:19:10,259 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,259 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,259 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,259 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,260 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-23 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 11.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 6.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 5.0}
2025-07-11 07:19:10,261 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,261 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,261 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,261 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,262 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-24 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 11.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 7.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-11 07:19:10,262 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,262 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,262 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,262 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,263 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-25 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 11.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 7.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 2.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 7.0}
2025-07-11 07:19:10,263 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,263 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,264 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,264 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,265 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-26 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 10.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 3.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 6.0, 'LINK/USDT': 2.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 3.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 12.0, 'DOT/USDT': 9.0}
2025-07-11 07:19:10,265 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,265 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,265 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,265 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,266 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-27 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 10.0, 'SOL/USDT': 0.0, 'SUI/USDT': 5.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 3.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 5.0, 'LINK/USDT': 2.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 3.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 12.0, 'DOT/USDT': 10.0}
2025-07-11 07:19:10,266 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,266 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,267 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,267 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,268 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-28 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 10.0, 'SOL/USDT': 1.0, 'SUI/USDT': 6.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 5.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 3.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 12.0, 'DOT/USDT': 11.0}
2025-07-11 07:19:10,268 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,268 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,268 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,268 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,269 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-01 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 10.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 6.0, 'LINK/USDT': 2.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 2.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 12.0, 'DOT/USDT': 11.0}
2025-07-11 07:19:10,269 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,269 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,270 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,270 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,271 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-02 00:00:00+00:00: {'ETH/USDT': 4.0, 'BTC/USDT': 8.0, 'SOL/USDT': 4.0, 'SUI/USDT': 5.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 13.0, 'LINK/USDT': 1.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 9.0, 'DOT/USDT': 9.0}
2025-07-11 07:19:10,271 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,271 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,271 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,271 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,272 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-03 00:00:00+00:00: {'ETH/USDT': 4.0, 'BTC/USDT': 8.0, 'SOL/USDT': 4.0, 'SUI/USDT': 5.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 13.0, 'LINK/USDT': 2.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 9.0, 'DOT/USDT': 9.0}
2025-07-11 07:19:10,272 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,272 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,273 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,273 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,274 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-04 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 1.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 13.0, 'LINK/USDT': 2.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-11 07:19:10,274 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,274 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,274 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,274 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,275 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-05 00:00:00+00:00: {'ETH/USDT': 4.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 1.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 7.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 13.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-11 07:19:10,275 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,275 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,275 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,275 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,276 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-06 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 1.0, 'SUI/USDT': 3.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 13.0, 'LINK/USDT': 8.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 7.0}
2025-07-11 07:19:10,277 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,277 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,277 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,277 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,278 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-07 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 1.0, 'SUI/USDT': 3.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 13.0, 'LINK/USDT': 8.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 7.0}
2025-07-11 07:19:10,278 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,278 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,278 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,278 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,279 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-08 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 1.0, 'SUI/USDT': 3.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 13.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-11 07:19:10,280 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,280 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,280 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,280 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,281 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-09 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 12.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-11 07:19:10,281 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,281 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,281 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,281 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,282 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-10 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 12.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-11 07:19:10,283 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,283 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,283 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,283 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,284 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-11 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 12.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-11 07:19:10,284 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,284 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,284 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,284 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,285 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-12 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 1.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 3.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 12.0, 'LINK/USDT': 5.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 4.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-11 07:19:10,286 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,286 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,286 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,286 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,287 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-13 00:00:00+00:00: {'ETH/USDT': 4.0, 'BTC/USDT': 9.0, 'SOL/USDT': 3.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 12.0, 'LINK/USDT': 5.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 4.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-11 07:19:10,287 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,287 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,287 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,287 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,288 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-14 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 12.0, 'LINK/USDT': 5.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 6.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-11 07:19:10,289 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,289 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,289 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,289 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,290 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-15 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 12.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 6.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 11.0, 'DOT/USDT': 8.0}
2025-07-11 07:19:10,290 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,290 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,290 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,290 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,291 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-16 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 10.0, 'LINK/USDT': 5.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 12.0, 'DOT/USDT': 8.0}
2025-07-11 07:19:10,292 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,292 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,292 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,292 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,293 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-17 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 9.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 10.0, 'LINK/USDT': 5.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 8.0}
2025-07-11 07:19:10,293 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,293 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,293 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,293 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,294 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-18 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 8.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 9.0, 'LINK/USDT': 5.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 9.0}
2025-07-11 07:19:10,295 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,295 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,295 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,295 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,296 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-19 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 8.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 9.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 12.0, 'DOT/USDT': 9.0}
2025-07-11 07:19:10,296 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,296 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,296 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,296 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,297 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-20 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 8.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 12.0, 'DOT/USDT': 9.0}
2025-07-11 07:19:10,298 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,298 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,298 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,298 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,299 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-21 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 8.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 9.0}
2025-07-11 07:19:10,299 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,299 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,299 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,299 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,300 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-22 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 8.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 9.0}
2025-07-11 07:19:10,301 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,301 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,301 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,301 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,302 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-23 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 6.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 9.0}
2025-07-11 07:19:10,302 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,302 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,302 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,302 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,303 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-24 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 0.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 13.0, 'DOT/USDT': 8.0}
2025-07-11 07:19:10,304 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,304 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,304 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,304 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,305 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-25 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 5.0, 'SOL/USDT': 4.0, 'SUI/USDT': 0.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 8.0}
2025-07-11 07:19:10,305 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,305 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,305 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,305 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,306 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-26 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 2.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 3.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 10.0, 'DOT/USDT': 9.0}
2025-07-11 07:19:10,307 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,307 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,307 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,307 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,308 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-27 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 3.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 3.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 9.0, 'DOT/USDT': 7.0}
2025-07-11 07:19:10,308 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,308 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,308 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,309 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,310 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-28 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 6.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 10.0, 'DOT/USDT': 6.0}
2025-07-11 07:19:10,310 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,310 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,311 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,311 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,312 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-29 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-11 07:19:10,312 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,313 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,313 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,313 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,314 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-30 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-11 07:19:10,315 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,315 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,315 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,315 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,316 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-31 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 2.0, 'SUI/USDT': 7.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-11 07:19:10,316 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,316 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,316 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,316 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,317 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-01 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 2.0, 'SUI/USDT': 7.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-11 07:19:10,317 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,318 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,318 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,318 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,319 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-02 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 2.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 8.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-11 07:19:10,319 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,320 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,320 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,320 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,321 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-03 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 10.0, 'SOL/USDT': 1.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 12.0, 'DOT/USDT': 7.0}
2025-07-11 07:19:10,321 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,321 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,322 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,322 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,323 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-04 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 3.0, 'SUI/USDT': 8.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-11 07:19:10,325 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,325 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,325 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,325 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,327 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-05 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 3.0, 'SUI/USDT': 8.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-11 07:19:10,327 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,328 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,328 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,328 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,330 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-06 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 3.0, 'SUI/USDT': 5.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 12.0, 'DOT/USDT': 8.0}
2025-07-11 07:19:10,330 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,331 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,331 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,331 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,332 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-07 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 4.0, 'SUI/USDT': 5.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 11.0, 'DOT/USDT': 7.0}
2025-07-11 07:19:10,333 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,333 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,333 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,333 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,335 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-08 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 5.0, 'SUI/USDT': 5.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 11.0, 'DOT/USDT': 5.0}
2025-07-11 07:19:10,335 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,335 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,335 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,336 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,337 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-09 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 6.0, 'SUI/USDT': 6.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 8.0, 'ADA/USDT': 1.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 11.0, 'DOT/USDT': 2.0}
2025-07-11 07:19:10,337 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,338 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,338 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,338 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,339 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-10 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 6.0, 'SUI/USDT': 6.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 11.0, 'DOT/USDT': 2.0}
2025-07-11 07:19:10,340 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,340 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,340 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,340 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,342 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-11 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 6.0, 'SUI/USDT': 6.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 2.0}
2025-07-11 07:19:10,342 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,342 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,342 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,343 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,344 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-12 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 7.0, 'DOT/USDT': 2.0}
2025-07-11 07:19:10,344 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,344 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,345 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,345 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,346 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-13 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 10.0, 'SOL/USDT': 8.0, 'SUI/USDT': 5.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 7.0, 'DOT/USDT': 2.0}
2025-07-11 07:19:10,346 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,347 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,347 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,347 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,348 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-14 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 8.0, 'SUI/USDT': 5.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 7.0, 'DOT/USDT': 2.0}
2025-07-11 07:19:10,348 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,349 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,349 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,349 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,350 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-15 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 8.0, 'SUI/USDT': 5.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-11 07:19:10,351 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,351 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,351 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,351 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,353 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-16 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 11.0, 'SUI/USDT': 4.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-11 07:19:10,353 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,353 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,353 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,354 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,355 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-17 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 12.0, 'SUI/USDT': 4.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-11 07:19:10,355 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,355 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,356 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,356 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,357 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-18 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 12.0, 'SUI/USDT': 4.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-11 07:19:10,357 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,358 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,358 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,358 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,359 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-19 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 13.0, 'SUI/USDT': 4.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 5.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-11 07:19:10,359 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,360 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,360 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,360 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,361 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-20 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 10.0, 'SOL/USDT': 13.0, 'SUI/USDT': 3.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 5.0}
2025-07-11 07:19:10,361 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,362 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,362 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,362 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,363 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-21 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 10.0, 'SOL/USDT': 13.0, 'SUI/USDT': 3.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 3.0}
2025-07-11 07:19:10,364 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,364 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,364 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,365 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,366 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-22 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 9.0, 'SOL/USDT': 12.0, 'SUI/USDT': 9.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 5.0, 'TRX/USDT': 6.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 5.0, 'DOT/USDT': 1.0}
2025-07-11 07:19:10,366 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,366 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,367 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,367 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,367 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-04-23:
2025-07-11 07:19:10,367 - [BITVAVO] - root - INFO -    Signal Date: 2025-04-22 (generated at 00:00 UTC)
2025-07-11 07:19:10,368 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-04-23 00:00 UTC (immediate)
2025-07-11 07:19:10,368 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-11 07:19:10,368 - [BITVAVO] - root - INFO -    Buying: ['SOL/USDT']
2025-07-11 07:19:10,368 - [BITVAVO] - root - INFO -    SOL/USDT buy price: $151.1000 (close price)
2025-07-11 07:19:10,370 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-23 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 8.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 8.0, 'TRX/USDT': 4.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 1.0}
2025-07-11 07:19:10,371 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,371 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,371 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,371 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,371 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-07-11 07:19:10,371 - [BITVAVO] - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-07-11 07:19:10,371 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-07-11 07:19:10,371 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-11 07:19:10,371 - [BITVAVO] - root - INFO -    Selling: ['SOL/USDT']
2025-07-11 07:19:10,371 - [BITVAVO] - root - INFO -    Buying: ['SUI/USDT']
2025-07-11 07:19:10,372 - [BITVAVO] - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-07-11 07:19:10,373 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-24 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 8.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 3.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-11 07:19:10,373 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,374 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,374 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,375 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,376 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-25 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 8.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-11 07:19:10,376 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,377 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,377 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,377 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,378 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-26 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-11 07:19:10,378 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,378 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,378 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,378 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,379 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-27 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-11 07:19:10,380 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,380 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,380 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,380 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,381 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-28 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 8.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-11 07:19:10,381 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,382 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,382 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,382 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,383 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-29 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 8.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-11 07:19:10,384 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,384 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,384 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,384 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,385 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-30 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-11 07:19:10,385 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,385 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,385 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,386 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,387 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-01 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-11 07:19:10,387 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,387 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,387 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,388 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,389 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-02 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 8.0, 'SOL/USDT': 10.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-11 07:19:10,389 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,390 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,390 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,390 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,392 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-03 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 6.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-11 07:19:10,392 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,392 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,392 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,393 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,394 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-04 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 9.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-11 07:19:10,394 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,395 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,395 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,395 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,396 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-05 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 9.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-11 07:19:10,397 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,397 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,397 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,397 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,398 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-06 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 10.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-11 07:19:10,399 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,399 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,399 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,399 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,400 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-07 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 10.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 0.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-11 07:19:10,401 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,401 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,401 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,401 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,403 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-08 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 6.0, 'SOL/USDT': 9.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-11 07:19:10,403 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,403 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,403 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,403 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,405 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-09 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 4.0, 'SOL/USDT': 9.0, 'SUI/USDT': 12.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 1.0, 'DOT/USDT': 7.0}
2025-07-11 07:19:10,405 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,405 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,405 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,406 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,406 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-05-10:
2025-07-11 07:19:10,406 - [BITVAVO] - root - INFO -    Signal Date: 2025-05-09 (generated at 00:00 UTC)
2025-07-11 07:19:10,406 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-10 00:00 UTC (immediate)
2025-07-11 07:19:10,406 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-11 07:19:10,406 - [BITVAVO] - root - INFO -    Selling: ['SUI/USDT']
2025-07-11 07:19:10,406 - [BITVAVO] - root - INFO -    Buying: ['PEPE/USDT']
2025-07-11 07:19:10,406 - [BITVAVO] - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-07-11 07:19:10,408 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-10 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 3.0, 'SOL/USDT': 7.0, 'SUI/USDT': 12.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-11 07:19:10,408 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,408 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,408 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,408 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,410 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-11 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 3.0, 'SOL/USDT': 7.0, 'SUI/USDT': 12.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-11 07:19:10,410 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,410 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,411 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,411 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,412 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-12 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 2.0, 'SOL/USDT': 7.0, 'SUI/USDT': 11.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-11 07:19:10,412 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,413 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,413 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,413 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,414 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-13 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 2.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-11 07:19:10,414 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,414 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,414 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,415 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,416 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-14 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 2.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-11 07:19:10,416 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,417 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,417 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,417 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,418 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-15 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 3.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-11 07:19:10,418 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,419 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,419 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,419 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,420 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-16 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 3.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-11 07:19:10,420 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,421 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,421 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,421 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,422 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-17 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 4.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-11 07:19:10,423 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,423 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,423 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,423 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,425 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-18 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 4.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-11 07:19:10,425 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,425 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,425 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,425 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,426 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-19 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 5.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-11 07:19:10,426 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,426 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,427 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,427 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,428 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-20 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-11 07:19:10,428 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,428 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,428 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,428 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,428 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-05-21:
2025-07-11 07:19:10,428 - [BITVAVO] - root - INFO -    Signal Date: 2025-05-20 (generated at 00:00 UTC)
2025-07-11 07:19:10,428 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-21 00:00 UTC (immediate)
2025-07-11 07:19:10,429 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-11 07:19:10,429 - [BITVAVO] - root - INFO -    Selling: ['PEPE/USDT']
2025-07-11 07:19:10,429 - [BITVAVO] - root - INFO -    Buying: ['AAVE/USDT']
2025-07-11 07:19:10,429 - [BITVAVO] - root - INFO -    AAVE/USDT buy price: $247.5400 (close price)
2025-07-11 07:19:10,430 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-21 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 6.0}
2025-07-11 07:19:10,431 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,431 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,431 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,431 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,432 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-22 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 1.0, 'DOT/USDT': 6.0}
2025-07-11 07:19:10,432 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,433 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,433 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,433 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,433 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-05-23:
2025-07-11 07:19:10,433 - [BITVAVO] - root - INFO -    Signal Date: 2025-05-22 (generated at 00:00 UTC)
2025-07-11 07:19:10,433 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-23 00:00 UTC (immediate)
2025-07-11 07:19:10,433 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-11 07:19:10,433 - [BITVAVO] - root - INFO -    Selling: ['AAVE/USDT']
2025-07-11 07:19:10,433 - [BITVAVO] - root - INFO -    Buying: ['PEPE/USDT']
2025-07-11 07:19:10,433 - [BITVAVO] - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-07-11 07:19:10,435 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-23 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 7.0, 'SOL/USDT': 9.0, 'SUI/USDT': 7.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 2.0, 'DOT/USDT': 6.0}
2025-07-11 07:19:10,435 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,435 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,435 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,435 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,437 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-24 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 7.0, 'SOL/USDT': 9.0, 'SUI/USDT': 6.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 2.0, 'LINK/USDT': 1.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 5.0, 'DOT/USDT': 4.0}
2025-07-11 07:19:10,437 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,437 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,437 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,437 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,437 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-05-25:
2025-07-11 07:19:10,437 - [BITVAVO] - root - INFO -    Signal Date: 2025-05-24 (generated at 00:00 UTC)
2025-07-11 07:19:10,437 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-25 00:00 UTC (immediate)
2025-07-11 07:19:10,438 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-11 07:19:10,438 - [BITVAVO] - root - INFO -    Selling: ['PEPE/USDT']
2025-07-11 07:19:10,438 - [BITVAVO] - root - INFO -    Buying: ['AAVE/USDT']
2025-07-11 07:19:10,438 - [BITVAVO] - root - INFO -    AAVE/USDT buy price: $269.2800 (close price)
2025-07-11 07:19:10,439 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-25 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 9.0, 'SUI/USDT': 3.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 5.0, 'DOT/USDT': 2.0}
2025-07-11 07:19:10,440 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,440 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,440 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,440 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,442 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-26 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 9.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 2.0, 'TRX/USDT': 4.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 6.0, 'DOT/USDT': 1.0}
2025-07-11 07:19:10,442 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,442 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,442 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,442 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,443 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-27 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 9.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 2.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 7.0, 'DOT/USDT': 1.0}
2025-07-11 07:19:10,443 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,443 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,444 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,444 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,445 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-28 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 7.0, 'SOL/USDT': 8.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 3.0, 'LINK/USDT': 2.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-11 07:19:10,445 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,445 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,445 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,445 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,447 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-29 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 3.0, 'LINK/USDT': 2.0, 'TRX/USDT': 6.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 9.0, 'DOT/USDT': 2.0}
2025-07-11 07:19:10,447 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,447 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,447 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,447 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,448 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-30 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 1.0}
2025-07-11 07:19:10,449 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,449 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,449 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,449 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,451 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-31 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 1.0}
2025-07-11 07:19:10,451 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,452 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,452 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,452 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,454 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-01 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 1.0}
2025-07-11 07:19:10,454 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,454 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,454 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,455 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,456 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-02 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 10.0, 'DOT/USDT': 1.0}
2025-07-11 07:19:10,456 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,457 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,457 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,457 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,458 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-03 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 10.0, 'DOT/USDT': 1.0}
2025-07-11 07:19:10,459 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,459 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,459 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,459 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,460 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-04 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 9.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-11 07:19:10,460 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,460 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,461 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,461 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,462 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-05 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 2.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 3.0}
2025-07-11 07:19:10,462 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,462 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,463 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,463 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,464 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-06 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 2.0}
2025-07-11 07:19:10,464 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,464 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,464 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,464 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,466 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-07 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 5.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 2.0}
2025-07-11 07:19:10,466 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,466 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,466 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,466 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,467 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-08 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 5.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 2.0}
2025-07-11 07:19:10,467 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,467 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,467 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,468 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,469 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-09 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 5.0, 'SUI/USDT': 1.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-11 07:19:10,469 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,469 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,469 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,469 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,470 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-10 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-11 07:19:10,470 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,470 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,471 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,471 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,472 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-11 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-11 07:19:10,472 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,472 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,472 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,472 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,473 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-12 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-11 07:19:10,474 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,475 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,475 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,475 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,476 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-13 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 5.0, 'SUI/USDT': 1.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-11 07:19:10,477 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,477 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,477 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,477 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,478 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-14 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 5.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-11 07:19:10,479 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,479 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,479 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,479 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,481 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-15 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-11 07:19:10,481 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,481 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,482 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,482 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,483 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-16 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 3.0}
2025-07-11 07:19:10,483 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,484 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,484 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,484 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,485 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-17 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 4.0}
2025-07-11 07:19:10,486 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,486 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,486 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,486 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,488 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-18 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 4.0}
2025-07-11 07:19:10,488 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,488 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,488 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,489 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,490 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-19 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 3.0}
2025-07-11 07:19:10,490 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,491 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,491 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,491 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,493 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-20 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 3.0}
2025-07-11 07:19:10,493 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,493 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,493 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,493 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-11 07:19:10,493 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-07-11 07:19:10,493 - [BITVAVO] - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-07-11 07:19:10,494 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-07-11 07:19:10,494 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-11 07:19:10,494 - [BITVAVO] - root - INFO -    Selling: ['AAVE/USDT']
2025-07-11 07:19:10,495 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-21 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 11.0, 'DOT/USDT': 4.0}
2025-07-11 07:19:10,495 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,496 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,496 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,496 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,497 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-22 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 11.0, 'DOT/USDT': 2.0}
2025-07-11 07:19:10,498 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,498 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,498 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,498 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,499 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-23 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-11 07:19:10,499 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,500 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,500 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,500 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,501 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-24 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-11 07:19:10,501 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,501 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,501 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,501 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,502 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-25 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-11 07:19:10,502 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,503 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,503 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,503 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,504 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-26 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 13.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-11 07:19:10,504 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,504 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,504 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,504 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,505 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-27 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 13.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 2.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-11 07:19:10,505 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,506 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,506 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,506 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,507 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-28 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 13.0, 'SOL/USDT': 7.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-11 07:19:10,508 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,508 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,508 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,508 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,509 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-29 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 10.0, 'DOT/USDT': 2.0}
2025-07-11 07:19:10,509 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,509 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,509 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,509 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,510 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-30 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 12.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-11 07:19:10,511 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,511 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,511 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,511 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,512 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-01 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-11 07:19:10,512 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,512 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,512 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,513 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,514 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-02 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-11 07:19:10,514 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,514 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,514 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,514 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,515 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-03 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-11 07:19:10,515 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,515 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,515 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,515 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,516 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-04 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-11 07:19:10,517 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,517 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,517 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,517 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,518 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-05 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-11 07:19:10,518 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,518 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,518 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,518 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,519 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-06 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 0.0}
2025-07-11 07:19:10,519 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,520 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,520 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,520 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,521 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-07 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 0.0}
2025-07-11 07:19:10,521 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,521 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,521 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,521 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,522 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-08 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 5.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 7.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 6.0, 'DOT/USDT': 0.0}
2025-07-11 07:19:10,522 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,523 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,523 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,523 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,524 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-09 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 5.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 1.0, 'LINK/USDT': 7.0, 'TRX/USDT': 9.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 4.0, 'DOT/USDT': 0.0}
2025-07-11 07:19:10,524 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-11 07:19:10,524 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-11 07:19:10,524 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-11 07:19:10,524 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-11 07:19:10,587 - [BITVAVO] - root - INFO - Entry trade at 2025-04-23 00:00:00+00:00: SOL/USDT
2025-07-11 07:19:10,587 - [BITVAVO] - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: SOL/USDT -> SUI/USDT
2025-07-11 07:19:10,587 - [BITVAVO] - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-07-11 07:19:10,588 - [BITVAVO] - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-07-11 07:19:10,588 - [BITVAVO] - root - INFO - Swap trade at 2025-05-23 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-07-11 07:19:10,588 - [BITVAVO] - root - INFO - Swap trade at 2025-05-25 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-07-11 07:19:10,588 - [BITVAVO] - root - INFO - Exit trade at 2025-06-21 00:00:00+00:00 from AAVE/USDT
2025-07-11 07:19:10,589 - [BITVAVO] - root - INFO - Total trades: 7 (Entries: 1, Exits: 1, Swaps: 5)
2025-07-11 07:19:10,592 - [BITVAVO] - root - INFO - Strategy execution completed in 0s
2025-07-11 07:19:10,592 - [BITVAVO] - root - INFO - DEBUG: self.elapsed_time = 0.3855323791503906 seconds
2025-07-11 07:19:10,609 - [BITVAVO] - root - INFO - Saved allocation history to allocation_history_1d_1d_with_mtpi_no_rebal_independent_imcumbent_2025-02-10.csv
2025-07-11 07:19:10,609 - [BITVAVO] - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-07-11 07:19:10,609 - [BITVAVO] - root - INFO - Assets included in buy-and-hold comparison:
2025-07-11 07:19:10,609 - [BITVAVO] - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-07-11 07:19:10,609 - [BITVAVO] - root - INFO -   BTC/USDT: First available date 2024-12-12
2025-07-11 07:19:10,610 - [BITVAVO] - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-07-11 07:19:10,610 - [BITVAVO] - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-07-11 07:19:10,610 - [BITVAVO] - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-07-11 07:19:10,610 - [BITVAVO] - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-07-11 07:19:10,610 - [BITVAVO] - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-07-11 07:19:10,610 - [BITVAVO] - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-07-11 07:19:10,610 - [BITVAVO] - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-07-11 07:19:10,610 - [BITVAVO] - root - INFO -   TRX/USDT: First available date 2024-12-12
2025-07-11 07:19:10,610 - [BITVAVO] - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-07-11 07:19:10,610 - [BITVAVO] - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-07-11 07:19:10,610 - [BITVAVO] - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-07-11 07:19:10,610 - [BITVAVO] - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-07-11 07:19:10,610 - [BITVAVO] - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-07-11 07:19:10,613 - [BITVAVO] - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:19:10,615 - [BITVAVO] - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:19:10,617 - [BITVAVO] - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:19:10,619 - [BITVAVO] - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:19:10,621 - [BITVAVO] - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:19:10,623 - [BITVAVO] - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:19:10,625 - [BITVAVO] - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:19:10,627 - [BITVAVO] - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:19:10,629 - [BITVAVO] - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:19:10,630 - [BITVAVO] - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:19:10,633 - [BITVAVO] - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:19:10,635 - [BITVAVO] - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:19:10,636 - [BITVAVO] - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:19:10,638 - [BITVAVO] - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-11 07:19:10,642 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 211 points
2025-07-11 07:19:10,642 - [BITVAVO] - root - INFO - ETH/USDT B&H total return: 10.90%
2025-07-11 07:19:10,646 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 211 points
2025-07-11 07:19:10,646 - [BITVAVO] - root - INFO - BTC/USDT B&H total return: 19.07%
2025-07-11 07:19:10,650 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 211 points
2025-07-11 07:19:10,650 - [BITVAVO] - root - INFO - SOL/USDT B&H total return: -18.02%
2025-07-11 07:19:10,653 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 211 points
2025-07-11 07:19:10,654 - [BITVAVO] - root - INFO - SUI/USDT B&H total return: 8.55%
2025-07-11 07:19:10,657 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 211 points
2025-07-11 07:19:10,657 - [BITVAVO] - root - INFO - XRP/USDT B&H total return: 5.09%
2025-07-11 07:19:10,660 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 211 points
2025-07-11 07:19:10,661 - [BITVAVO] - root - INFO - AAVE/USDT B&H total return: 22.23%
2025-07-11 07:19:10,664 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 211 points
2025-07-11 07:19:10,665 - [BITVAVO] - root - INFO - AVAX/USDT B&H total return: -19.36%
2025-07-11 07:19:10,669 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 211 points
2025-07-11 07:19:10,669 - [BITVAVO] - root - INFO - ADA/USDT B&H total return: -4.86%
2025-07-11 07:19:10,672 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 211 points
2025-07-11 07:19:10,673 - [BITVAVO] - root - INFO - LINK/USDT B&H total return: -19.03%
2025-07-11 07:19:10,675 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 211 points
2025-07-11 07:19:10,676 - [BITVAVO] - root - INFO - TRX/USDT B&H total return: 19.37%
2025-07-11 07:19:10,678 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 211 points
2025-07-11 07:19:10,679 - [BITVAVO] - root - INFO - PEPE/USDT B&H total return: 29.41%
2025-07-11 07:19:10,682 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 211 points
2025-07-11 07:19:10,682 - [BITVAVO] - root - INFO - DOGE/USDT B&H total return: -23.63%
2025-07-11 07:19:10,685 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 211 points
2025-07-11 07:19:10,685 - [BITVAVO] - root - INFO - BNB/USDT B&H total return: 10.85%
2025-07-11 07:19:10,688 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 211 points
2025-07-11 07:19:10,689 - [BITVAVO] - root - INFO - DOT/USDT B&H total return: -19.90%
2025-07-11 07:19:10,692 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-11 07:19:10,704 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:19:10,718 - [BITVAVO] - root - INFO - Using colored segments for single-asset strategy visualization
2025-07-11 07:19:10,860 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-11 07:19:10,873 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:19:13,738 - [BITVAVO] - root - INFO - Added ETH/USDT buy-and-hold curve with 211 points
2025-07-11 07:19:13,739 - [BITVAVO] - root - INFO - Added BTC/USDT buy-and-hold curve with 211 points
2025-07-11 07:19:13,739 - [BITVAVO] - root - INFO - Added SOL/USDT buy-and-hold curve with 211 points
2025-07-11 07:19:13,739 - [BITVAVO] - root - INFO - Added SUI/USDT buy-and-hold curve with 211 points
2025-07-11 07:19:13,739 - [BITVAVO] - root - INFO - Added XRP/USDT buy-and-hold curve with 211 points
2025-07-11 07:19:13,739 - [BITVAVO] - root - INFO - Added AAVE/USDT buy-and-hold curve with 211 points
2025-07-11 07:19:13,739 - [BITVAVO] - root - INFO - Added AVAX/USDT buy-and-hold curve with 211 points
2025-07-11 07:19:13,739 - [BITVAVO] - root - INFO - Added ADA/USDT buy-and-hold curve with 211 points
2025-07-11 07:19:13,739 - [BITVAVO] - root - INFO - Added LINK/USDT buy-and-hold curve with 211 points
2025-07-11 07:19:13,739 - [BITVAVO] - root - INFO - Added TRX/USDT buy-and-hold curve with 211 points
2025-07-11 07:19:13,739 - [BITVAVO] - root - INFO - Added PEPE/USDT buy-and-hold curve with 211 points
2025-07-11 07:19:13,739 - [BITVAVO] - root - INFO - Added DOGE/USDT buy-and-hold curve with 211 points
2025-07-11 07:19:13,739 - [BITVAVO] - root - INFO - Added BNB/USDT buy-and-hold curve with 211 points
2025-07-11 07:19:13,739 - [BITVAVO] - root - INFO - Added DOT/USDT buy-and-hold curve with 211 points
2025-07-11 07:19:13,739 - [BITVAVO] - root - INFO - Added 14 buy-and-hold curves to results
2025-07-11 07:19:13,745 - [BITVAVO] - root - INFO -   - ETH/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:19:13,745 - [BITVAVO] - root - INFO -   - BTC/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:19:13,745 - [BITVAVO] - root - INFO -   - SOL/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:19:13,745 - [BITVAVO] - root - INFO -   - SUI/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:19:13,746 - [BITVAVO] - root - INFO -   - XRP/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:19:13,746 - [BITVAVO] - root - INFO -   - AAVE/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:19:13,746 - [BITVAVO] - root - INFO -   - AVAX/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:19:13,746 - [BITVAVO] - root - INFO -   - ADA/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:19:13,746 - [BITVAVO] - root - INFO -   - LINK/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:19:13,746 - [BITVAVO] - root - INFO -   - TRX/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:19:13,746 - [BITVAVO] - root - INFO -   - PEPE/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:19:13,746 - [BITVAVO] - root - INFO -   - DOGE/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:19:13,746 - [BITVAVO] - root - INFO -   - BNB/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:19:13,746 - [BITVAVO] - root - INFO -   - DOT/USDT: 211 points from 2024-12-12 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:19:13,788 - [BITVAVO] - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-07-11 07:19:13,789 - [BITVAVO] - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-11 07:19:13,791 - [BITVAVO] - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-07-11 07:19:13,791 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-11 07:19:13,804 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-11 07:19:13,804 - [BITVAVO] - root - INFO - Loaded MTPI configuration with 8 enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-11 07:19:13,804 - [BITVAVO] - root - INFO - Using MTPI indicators from config: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-11 07:19:13,804 - [BITVAVO] - root - INFO - Combination method: consensus
2025-07-11 07:19:13,805 - [BITVAVO] - root - INFO - Calculated appropriate limit for 1d timeframe: 216 candles (minimum 180.0 candles needed for 60 length indicator)
2025-07-11 07:19:13,805 - [BITVAVO] - root - INFO - Checking cache for 1 symbols (1d)...
2025-07-11 07:19:13,866 - [BITVAVO] - root - INFO - Loaded 2152 rows of BTC/USDT data from cache (last updated: 2025-07-11)
2025-07-11 07:19:13,869 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-11
2025-07-11 07:19:13,870 - [BITVAVO] - root - INFO - Loaded 2152 rows of BTC/USDT data from cache (after filtering).
2025-07-11 07:19:13,870 - [BITVAVO] - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-11 07:19:13,870 - [BITVAVO] - root - INFO - Fetched BTC data: 2152 candles from 2019-08-20 00:00:00+00:00 to 2025-07-10 00:00:00+00:00
2025-07-11 07:19:13,871 - [BITVAVO] - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-07-11 07:19:14,217 - [BITVAVO] - root - INFO - Generated PGO Score signals: {-1: np.int64(996), 0: np.int64(34), 1: np.int64(1122)}
2025-07-11 07:19:14,217 - [BITVAVO] - root - INFO - PGO signal: 1
2025-07-11 07:19:14,217 - [BITVAVO] - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-07-11 07:19:14,321 - [BITVAVO] - root - INFO - Generated BB Score signals: {-1: np.int64(993), 0: np.int64(33), 1: np.int64(1126)}
2025-07-11 07:19:14,322 - [BITVAVO] - root - INFO - Bollinger Bands signal: 1
2025-07-11 07:19:19,477 - [BITVAVO] - root - INFO - Generated DWMA signals using Weighted SD method
2025-07-11 07:19:19,478 - [BITVAVO] - root - INFO - DWMA Score signal: 1
2025-07-11 07:19:20,065 - [BITVAVO] - root - INFO - Generated DEMA Supertrend signals
2025-07-11 07:19:20,066 - [BITVAVO] - root - INFO - Signal distribution: {-1: np.int64(1268), 0: np.int64(1), 1: np.int64(883)}
2025-07-11 07:19:20,066 - [BITVAVO] - root - INFO - Generated DEMA Super Score signals
2025-07-11 07:19:20,066 - [BITVAVO] - root - INFO - DEMA Super Score signal: 1
2025-07-11 07:19:21,665 - [BITVAVO] - root - INFO - Generated DPSD signals
2025-07-11 07:19:21,665 - [BITVAVO] - root - INFO - Signal distribution: {-1: np.int64(1087), 0: np.int64(87), 1: np.int64(978)}
2025-07-11 07:19:21,665 - [BITVAVO] - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-07-11 07:19:21,665 - [BITVAVO] - root - INFO - DPSD Score signal: 1
2025-07-11 07:19:21,755 - [BITVAVO] - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-07-11 07:19:21,755 - [BITVAVO] - root - INFO - Generated AAD Score signals using SMA method
2025-07-11 07:19:21,756 - [BITVAVO] - root - INFO - AAD Score signal: 1
2025-07-11 07:19:22,518 - [BITVAVO] - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-07-11 07:19:22,518 - [BITVAVO] - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-07-11 07:19:22,519 - [BITVAVO] - root - INFO - Dynamic EMA Score signal: 1
2025-07-11 07:19:23,957 - [BITVAVO] - root - INFO - Quantile DEMA Score signal: 1
2025-07-11 07:19:23,958 - [BITVAVO] - root - INFO - Individual signals: {'pgo': 1, 'bollinger_bands': 1, 'dwma_score': 1, 'dema_super_score': 1, 'dpsd_score': 1, 'aad_score': 1, 'dynamic_ema_score': 1, 'quantile_dema_score': 1}
2025-07-11 07:19:23,958 - [BITVAVO] - root - INFO - Combined MTPI signal (consensus): 1
2025-07-11 07:19:23,958 - [BITVAVO] - root - INFO - MTPI Score: 1.000000
2025-07-11 07:19:23,958 - [BITVAVO] - root - INFO - Added current MTPI score to results: 1.000000 (using 1d timeframe)
2025-07-11 07:19:23,962 - [BITVAVO] - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-07-11 07:19:23,962 - [BITVAVO] - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 4.0, 'SUI/USDT': 11.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 3.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 0.0}
2025-07-11 07:19:23,962 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 4.0, 'SUI/USDT': 11.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 3.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 0.0}
2025-07-11 07:19:23,962 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-11 07:19:23,963 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-11 07:19:23,963 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 9.0)
2025-07-11 07:19:23,963 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - BTC/USDT -> BTC/EUR (score: 3.0)
2025-07-11 07:19:23,963 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 4.0)
2025-07-11 07:19:23,963 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 11.0)
2025-07-11 07:19:23,963 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 10.0)
2025-07-11 07:19:23,963 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 10.0)
2025-07-11 07:19:23,963 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 1.0)
2025-07-11 07:19:23,964 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 3.0)
2025-07-11 07:19:23,964 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 8.0)
2025-07-11 07:19:23,964 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - TRX/USDT -> TRX/EUR (score: 1.0)
2025-07-11 07:19:23,964 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 9.0)
2025-07-11 07:19:23,964 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 6.0)
2025-07-11 07:19:23,964 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 0.0)
2025-07-11 07:19:23,964 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 0.0)
2025-07-11 07:19:23,964 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 4.0, 'SUI/EUR': 11.0, 'XRP/EUR': 10.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 1.0, 'ADA/EUR': 3.0, 'LINK/EUR': 8.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 6.0, 'BNB/EUR': 0.0, 'DOT/EUR': 0.0}
2025-07-11 07:19:23,964 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 07:19:23,964 - [BITVAVO] - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 4.0, 'SUI/EUR': 11.0, 'XRP/EUR': 10.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 1.0, 'ADA/EUR': 3.0, 'LINK/EUR': 8.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 6.0, 'BNB/EUR': 0.0, 'DOT/EUR': 0.0}
2025-07-11 07:19:23,974 - [BITVAVO] - root - INFO - Saved metrics to new file: Performance_Metrics/metrics_BestAsset_1d_1d_assets14_since_20250704_run_20250711_071849.csv
2025-07-11 07:19:23,974 - [BITVAVO] - root - INFO - Saved performance metrics to CSV: Performance_Metrics/metrics_BestAsset_1d_1d_assets14_since_20250704_run_20250711_071849.csv
2025-07-11 07:19:23,974 - [BITVAVO] - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-07-11 07:19:23,974 - [BITVAVO] - root - INFO - Results type: <class 'dict'>
2025-07-11 07:19:23,974 - [BITVAVO] - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-07-11 07:19:23,974 - [BITVAVO] - root - INFO - Success flag set to: True
2025-07-11 07:19:23,974 - [BITVAVO] - root - INFO - Message set to: Strategy calculation completed successfully
2025-07-11 07:19:23,974 - [BITVAVO] - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 211 entries
2025-07-11 07:19:23,975 - [BITVAVO] - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-07-11 07:19:23,975 - [BITVAVO] - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 211 entries
2025-07-11 07:19:23,975 - [BITVAVO] - root - INFO -   - mtpi_signals: <class 'pandas.core.series.Series'> with 271 entries
2025-07-11 07:19:23,975 - [BITVAVO] - root - INFO -   - mtpi_score: <class 'float'>
2025-07-11 07:19:23,975 - [BITVAVO] - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 211 entries
2025-07-11 07:19:23,975 - [BITVAVO] - root - INFO -   - performance_metrics: dict with 3 entries
2025-07-11 07:19:23,975 - [BITVAVO] - root - INFO -   - metrics_file: <class 'str'>
2025-07-11 07:19:23,975 - [BITVAVO] - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-07-11 07:19:23,975 - [BITVAVO] - root - INFO -   - success: <class 'bool'>
2025-07-11 07:19:23,975 - [BITVAVO] - root - INFO -   - message: <class 'str'>
2025-07-11 07:19:23,975 - [BITVAVO] - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-07-11 07:19:23,975 - [BITVAVO] - root - INFO - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: 
2025-07-11 07:19:23,977 - [BITVAVO] - root - INFO - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-07-06 00:00:00+00:00    
2025-07-07 00:00:00+00:00    
2025-07-08 00:00:00+00:00    
2025-07-09 00:00:00+00:00    
2025-07-10 00:00:00+00:00    
dtype: object
2025-07-11 07:19:23,977 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Latest scores available: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 4.0, 'SUI/EUR': 11.0, 'XRP/EUR': 10.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 1.0, 'ADA/EUR': 3.0, 'LINK/EUR': 8.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 6.0, 'BNB/EUR': 0.0, 'DOT/EUR': 0.0}
2025-07-11 07:19:23,977 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Scores dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 07:19:23,977 - [BITVAVO] - root - INFO - [DEBUG] TIE-BREAKING - Strategy: imcumbent
2025-07-11 07:19:23,977 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - find_best_asset_for_day() called with 14 assets
2025-07-11 07:19:23,977 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Input scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 4.0, 'SUI/EUR': 11.0, 'XRP/EUR': 10.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 1.0, 'ADA/EUR': 3.0, 'LINK/EUR': 8.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 6.0, 'BNB/EUR': 0.0, 'DOT/EUR': 0.0}
2025-07-11 07:19:23,977 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 07:19:23,977 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Maximum score found: 11.0
2025-07-11 07:19:23,977 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Assets with max score 11.0: ['SUI/EUR']
2025-07-11 07:19:23,977 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - No tie detected, single winner: SUI/EUR
2025-07-11 07:19:23,977 - [BITVAVO] - root - ERROR - [DEBUG] SELECTED BEST ASSET: SUI/EUR (score: 11.0)
2025-07-11 07:19:23,978 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Current day's best asset: SUI/EUR (MTPI signal: 1)
2025-07-11 07:19:23,978 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Maximum score: 11.0
2025-07-11 07:19:23,978 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Assets with max score: ['SUI/EUR']
2025-07-11 07:19:23,978 - [BITVAVO] - root - ERROR - [DEBUG] NO TIE - UPDATING SELECTION:  -> SUI/EUR
2025-07-11 07:19:23,978 - [BITVAVO] - root - ERROR - [DEBUG] NO TIE - Single winner: SUI/EUR
2025-07-11 07:19:24,010 - [BITVAVO] - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-07-11 07:19:24,010 - [BITVAVO] - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-11 07:19:24,010 - [BITVAVO] - root - INFO - Single asset strategy with best asset: SUI/EUR
2025-07-11 07:19:24,010 - [BITVAVO] - root - INFO - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-07-11 07:19:24,010 - [BITVAVO] - root - INFO - [DEBUG]   - Best asset selected: SUI/EUR
2025-07-11 07:19:24,011 - [BITVAVO] - root - INFO - [DEBUG]   - Assets held: {'SUI/EUR': 1.0}
2025-07-11 07:19:24,011 - [BITVAVO] - root - INFO - [DEBUG]   - MTPI signal: 1
2025-07-11 07:19:24,011 - [BITVAVO] - root - INFO - [DEBUG]   - Use MTPI signal: True
2025-07-11 07:19:24,011 - [BITVAVO] - root - ERROR - 🚨 ? SUI/EUR WAS SELECTED
2025-07-11 07:19:24,011 - [BITVAVO] - root - INFO - Executing single-asset strategy with best asset: SUI/EUR
2025-07-11 07:19:24,011 - [BITVAVO] - root - INFO - Executing strategy signal: best_asset=SUI/EUR, mtpi_signal=1, mode=live
2025-07-11 07:19:24,178 - [BITVAVO] - root - INFO - Incremented daily trade counter for SUI/EUR: 1/5
2025-07-11 07:19:24,178 - [BITVAVO] - root - INFO - ASSET SELECTION - Attempting to enter position for selected asset: SUI/EUR
2025-07-11 07:19:24,178 - [BITVAVO] - root - INFO - Attempting to enter position for SUI/EUR in live mode
2025-07-11 07:19:24,178 - [BITVAVO] - root - INFO - [DEBUG] TRADE - SUI/EUR: Starting enter_position attempt
2025-07-11 07:19:24,178 - [BITVAVO] - root - INFO - [DEBUG] TRADE - SUI/EUR: Trading mode: live
2025-07-11 07:19:24,179 - [BITVAVO] - root - INFO - TRADE ATTEMPT - SUI/EUR: Getting current market price...
2025-07-11 07:19:24,179 - [BITVAVO] - root - INFO - [DEBUG] PRICE - SUI/EUR: Starting get_current_price
2025-07-11 07:19:24,179 - [BITVAVO] - root - INFO - [DEBUG] PRICE - SUI/EUR: Exchange ID: bitvavo
2025-07-11 07:19:24,179 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Initializing exchange bitvavo
2025-07-11 07:19:24,190 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Exchange initialized successfully
2025-07-11 07:19:24,190 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Exchange markets not loaded, loading now...
2025-07-11 07:19:24,667 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Symbol found after loading markets
2025-07-11 07:19:24,667 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Attempting to fetch ticker...
2025-07-11 07:19:24,716 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker fetched successfully
2025-07-11 07:19:24,716 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker data: {'symbol': 'SUI/EUR', 'timestamp': 1752218362308, 'datetime': '2025-07-11T07:19:22.308Z', 'high': 3.0319, 'low': 2.7271, 'bid': 2.9531, 'bidVolume': 239.30933156, 'ask': 2.9569, 'askVolume': 580.10033034, 'vwap': 2.876070958178728, 'open': 2.7528, 'close': 2.9549, 'last': 2.9549, 'previousClose': None, 'change': 0.2021, 'percentage': 7.341615809357744, 'average': 2.85385, 'baseVolume': 2878168.20653743, 'quoteVolume': 8277815.991575657, 'info': {'market': 'SUI-EUR', 'startTimestamp': '1752131962308', 'timestamp': '1752218362308', 'open': '2.7528', 'openTimestamp': '1752131984866', 'high': '3.0319', 'low': '2.7271', 'last': '2.9549', 'closeTimestamp': '1752218345794', 'bid': '2.953100', 'bidSize': '239.30933156', 'ask': '2.956900', 'askSize': '580.10033034', 'volume': '2878168.20653743', 'volumeQuote': '8277815.991575657205'}, 'indexPrice': None, 'markPrice': None}
2025-07-11 07:19:24,717 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Last price: 2.9549
2025-07-11 07:19:24,717 - [BITVAVO] - root - INFO - [DEBUG] TRADE - SUI/EUR: get_current_price returned: 2.9549
2025-07-11 07:19:24,717 - [BITVAVO] - root - INFO - [DEBUG] TRADE - SUI/EUR: Price type: <class 'float'>
2025-07-11 07:19:24,717 - [BITVAVO] - root - INFO - [DEBUG] TRADE - SUI/EUR: Price evaluation - not price: False
2025-07-11 07:19:24,718 - [BITVAVO] - root - INFO - [DEBUG] TRADE - SUI/EUR: Price evaluation - price <= 0: False
2025-07-11 07:19:24,718 - [BITVAVO] - root - INFO - TRADE ATTEMPT - SUI/EUR: Current price: 2.********
2025-07-11 07:19:24,788 - [BITVAVO] - root - INFO - Available balance for EUR: 4544.********
2025-07-11 07:19:24,793 - [BITVAVO] - root - INFO - Loaded market info for 176 trading pairs
2025-07-11 07:19:24,794 - [BITVAVO] - root - INFO - Calculated position size for SUI/EUR: 15.******** (using 1% of 4544.82, accounting for fees)
2025-07-11 07:19:24,794 - [BITVAVO] - root - INFO - Calculated position size: 15.******** SUI
2025-07-11 07:19:24,794 - [BITVAVO] - root - INFO - Entering position for SUI/EUR: 15.******** units at 2.******** (value: 45.******** EUR)
2025-07-11 07:19:24,795 - [BITVAVO] - root - INFO - Executing live market buy order for SUI/EUR
2025-07-11 07:19:24,849 - [BITVAVO] - root - INFO - Adjusted base amount for SUI/EUR: 15.******** -> 15.********
2025-07-11 07:19:24,896 - [BITVAVO] - root - ERROR - Error creating market buy order: [<class 'decimal.ConversionSyntax'>]
2025-07-11 07:19:24,897 - [BITVAVO] - root - ERROR - TRADE FAILURE - SUI/EUR: Failed to create order for SUI/EUR
2025-07-11 07:19:24,899 - [BITVAVO] - root - ERROR - Trade failed: BUY SUI/EUR, amount=15.********, price=2.********, reason=Failed to create order for SUI/EUR
2025-07-11 07:19:24,900 - [BITVAVO] - root - ERROR - Trade failed: UNKNOWN SUI/EUR, amount=0.********, price=0.********, reason=Failed to create order for SUI/EUR
2025-07-11 07:19:24,900 - [BITVAVO] - root - ERROR - TRADE FAILURE - SUI/EUR: Failed to enter position
2025-07-11 07:19:24,900 - [BITVAVO] - root - ERROR - TRADE FAILURE - SUI/EUR: Error type: order_creation_failed
2025-07-11 07:19:24,900 - [BITVAVO] - root - ERROR - TRADE FAILURE - SUI/EUR: Error reason: Failed to create order for SUI/EUR
2025-07-11 07:19:24,901 - [BITVAVO] - root - WARNING - HIGH-PRIORITY ASSET REJECTED: SUI/EUR - Failed to create order for SUI/EUR
2025-07-11 07:19:24,901 - [BITVAVO] - root - ERROR - Trade failed: UNKNOWN SUI/EUR, amount=0.********, price=0.********, reason=Failed to create order for SUI/EUR
2025-07-11 07:19:24,901 - [BITVAVO] - root - INFO - Single-asset trade result logged to trade log file
2025-07-11 07:19:24,902 - [BITVAVO] - root - ERROR - Trade execution failed: {'success': False, 'reason': 'Failed to create order for SUI/EUR', 'symbol': 'SUI/EUR', 'error_type': 'order_creation_failed'}
2025-07-11 07:19:24,943 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 400 Bad Request"
2025-07-11 07:19:24,944 - [BITVAVO] - root - WARNING - Failed to send with Markdown formatting: Can't parse entities: can't find end of the entity starting at byte offset 92
2025-07-11 07:19:24,986 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-11 07:19:24,989 - [BITVAVO] - root - INFO - Asset selection logged: 1 assets selected with single_asset allocation
2025-07-11 07:19:24,990 - [BITVAVO] - root - INFO - Asset scores (sorted by score):
2025-07-11 07:19:24,990 - [BITVAVO] - root - INFO -   SUI/EUR: score=11.0, status=SELECTED, weight=1.00
2025-07-11 07:19:24,990 - [BITVAVO] - root - INFO -   XRP/EUR: score=10.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:19:24,990 - [BITVAVO] - root - INFO -   AAVE/EUR: score=10.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:19:24,991 - [BITVAVO] - root - INFO -   ETH/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:19:24,991 - [BITVAVO] - root - INFO -   PEPE/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:19:24,991 - [BITVAVO] - root - INFO -   LINK/EUR: score=8.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:19:24,991 - [BITVAVO] - root - INFO -   DOGE/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:19:24,991 - [BITVAVO] - root - INFO -   SOL/EUR: score=4.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:19:24,991 - [BITVAVO] - root - INFO -   BTC/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:19:24,991 - [BITVAVO] - root - INFO -   ADA/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:19:24,991 - [BITVAVO] - root - INFO -   AVAX/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:19:24,992 - [BITVAVO] - root - INFO -   TRX/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:19:24,992 - [BITVAVO] - root - INFO -   BNB/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:19:24,992 - [BITVAVO] - root - INFO -   DOT/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-07-11 07:19:24,993 - [BITVAVO] - root - INFO - Asset selection logged with 14 assets scored and 1 assets selected
2025-07-11 07:19:24,994 - [BITVAVO] - root - INFO - Extracted asset scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 4.0, 'SUI/EUR': 11.0, 'XRP/EUR': 10.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 1.0, 'ADA/EUR': 3.0, 'LINK/EUR': 8.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 6.0, 'BNB/EUR': 0.0, 'DOT/EUR': 0.0}
2025-07-11 07:19:25,042 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-11 07:19:25,045 - [BITVAVO] - root - INFO - Strategy execution completed successfully in 36.04 seconds
2025-07-11 07:19:25,049 - [BITVAVO] - root - INFO - Saved recovery state to data/state/recovery_state.json
2025-07-11 07:23:19,726 - [BITVAVO] - root - INFO - Received signal 2, shutting down...
2025-07-11 07:23:20,740 - [BITVAVO] - root - INFO - Received signal 2, shutting down...
2025-07-11 07:23:20,741 - [BITVAVO] - root - WARNING - Service is not running
